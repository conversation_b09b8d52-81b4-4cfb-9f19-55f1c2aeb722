---
tags:
  - 计算机/操作系统/Linux
  - 操作系统/Linux
  - 操作系统/macOS
status: 尚未完成
---

# Linux 中的 `XDG_CONFIG_HOME`

## 参考资料

- [XDG Base Directory Specification](https://specifications.freedesktop.org/basedir-spec/latest/)
- [Environment variables](https://specifications.freedesktop.org/basedir-spec/latest/ar01s03.html)
- [macos - Equivalents of XDG_CONFIG_HOME and XDG_DATA_HOME on Mac OS X? - Stack Overflow](https://stackoverflow.com/a/5084892/19954520)
- [adrg/xdg: Go implementation of the XDG Base Directory Specification and XDG user directories](https://github.com/adrg/xdg)
