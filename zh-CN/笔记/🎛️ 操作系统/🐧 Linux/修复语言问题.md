---
tags:
  - 操作系统
  - 操作系统/Linux
  - 命令行/终端
  - 操作系统/Debian
  - 操作系统/Debian/Debian-11
  - 操作系统/Debian/Debian-12
---
## 修复语言问题

如果遇到这样的问题

```shell
locale: Cannot set LC_CTYPE to default locale: No such file or directory
locale: Cannot set LC_ALL to default locale: No such file or directory
```

或者这样

```shell
警告：setlocale: LC_ALL: 无法改变区域设置 (en_US.UTF-8)：没有那个文件或目录
```

或者这样

```shell
perl: warning: Setting locale failed.
perl: warning: Please check that your locale settings:
	LANGUAGE = "zh_CN:zh",
	LC_ALL = (unset),
	LC_CTYPE = "UTF-8",
	LC_TERMINAL = "iTerm2",
	LANG = "zh_CN.UTF-8"
    are supported and installed on your system.
perl: warning: Falling back to a fallback locale ("zh_CN.UTF-8").
locale: Cannot set LC_CTYPE to default locale: No such file or directory
locale: ??? LC_ALL ????????: ?????????
```

核心的三个东西是：

- `locale`
- `localectl`
- `env | grep LC`

我们应该按照这样的流程进行检查：

```shell
locale -a
localectl list-locales

# 如果是 debian 系的话可以这样开启语言配置程序
sudo dpkg-reconfigure locales
localectl list-locales

# 如果连 locales 都显示不了，可以手动安装一下看看
sudo apt purge locales && sudo apt install locales
localectl list-locales

# 可以手动编辑一下系统配置
sudo vim /etc/locale.gen
sudo locale-gen

locale -a
localectl list-locales
```

```shell
localectl list-locales
C.UTF-8
en_US.UTF-8
zh_CN.UTF-8
```

```shell
locale -a
C
C.utf8
en_US.utf8
POSIX
zh_CN.utf
```

也可以在 env 里面查找

```shell
LANG=en_US.UTF-8
LANGUAGE=
LC_CTYPE=en_US.UTF-8
LC_NUMERIC="en_US.UTF-8"
LC_TIME="en_US.UTF-8"
LC_COLLATE="en_US.UTF-8"
LC_MONETARY="en_US.UTF-8"
LC_MESSAGES="en_US.UTF-8"
LC_PAPER="en_US.UTF-8"
LC_NAME="en_US.UTF-8"
LC_ADDRESS="en_US.UTF-8"
LC_TELEPHONE="en_US.UTF-8"
LC_MEASUREMENT="en_US.UTF-8"
LC_IDENTIFICATION="en_US.UTF-8"
LC_ALL=
```

我们也可以手动指定：

```shell
sudo vim /etc/locale.conf
```

```shell
LANG=en_US.UTF-8
LC_CTYPE=en_US.UTF-8
```

或者按照不同的用户进行指定：

```shell
vim .bashrc
```

```shell
export LANG=en_US.UTF-8
export LC_CTYPE=en_US.UTF-8
```