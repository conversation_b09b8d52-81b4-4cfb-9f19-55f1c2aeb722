---
tags:
  - 软件/Obsidian/插件
  - 软件/Obsidian
---
### 待办事项清单

项目名称：delashum/obsidian-checklist-plugin
GitHub 仓库地址： [https://github.com/delashum/obsidian-checklist-plugin](https://github.com/delashum/obsidian-checklist-plugin)

### 分栏浏览

项目名称：deathau/sliding-panes-obsidian
项目介绍：Andy <PERSON> Mode as a plugin
GitHub 仓库地址： [https://github.com/deathau/sliding-panes-obsidian](https://github.com/deathau/sliding-panes-obsidian)

### 日历

项目名称：liamcain/obsidian-calendar-plugin
项目介绍：Simple calendar widget for Obsidian.
GitHub 仓库地址： [https://github.com/liamcain/obsidian-calendar-plugin](https://github.com/liamcain/obsidian-calendar-plugin)

### 大日历

项目名称：Quorafind/Obsidian-Big-Calendar
项目介绍：Big Calendar in Obsidian, for manage your events in a day/week/month and see agenda too!
GitHub 仓库地址： [https://github.com/Quorafind/Obsidian-Big-Calendar](https://github.com/Quorafind/Obsidian-Big-Calendar)

### 格式化小助手

项目名称：Reocin/obsidian-markdown-formatting-assistant-plugin
项目介绍：: This Plugin provides a simple WYSIWYG Editor for Markdown and in addition a command line interface. The command line interface facilitate a faster workflow.
GitHub 仓库地址： [https://github.com/Reocin/obsidian-markdown-formatting-assistant-plugin](https://github.com/Reocin/obsidian-markdown-formatting-assistant-plugin)

### 改进过的表格导航、格式化和编辑

项目名称：tgrosinger/advanced-tables-obsidian
项目介绍：Improved table navigation, formatting, and manipulation in Obsidian.md
GitHub 仓库地址： [https://github.com/tgrosinger/advanced-tables-obsidian](https://github.com/tgrosinger/advanced-tables-obsidian)

### 计划每一天

项目名称：ivan-lednev/obsidian-day-planner
项目介绍：An Obsidian plugin for day planning and managing pomodoro timers from a task list in a Markdown note.
GitHub 仓库地址： [https://github.com/ivan-lednev/obsidian-day-planner](https://github.com/ivan-lednev/obsidian-day-planner)

### 本地搜索引擎，改进搜索

项目名称：scambier/obsidian-omnisearch
项目介绍：A search engine that "just works" for Obsidian. Supports OCR and PDF indexing.
GitHub 仓库地址： [https://github.com/scambier/obsidian-omnisearch](https://github.com/scambier/obsidian-omnisearch)

### 闪卡和闪卡复习，对抗遗忘曲线

项目名称：st3v3nmw/obsidian-spaced-repetition
项目介绍：Fight the forgetting curve by reviewing flashcards & entire notes on Obsidian.md
GitHub 仓库地址： [https://github.com/st3v3nmw/obsidian-spaced-repetition](https://github.com/st3v3nmw/obsidian-spaced-repetition)

### 在 Obsidian 中绘制图表

项目名称：phibr0/obsidian-charts
项目介绍：Charts - Obsidian Plugin | Create editable, interactive and animated Charts in Obsidian via Chart.js
GitHub 仓库地址： [https://github.com/phibr0/obsidian-charts](https://github.com/phibr0/obsidian-charts)

### 悬浮预览编辑器

项目名称：nothingislost/obsidian-hover-editor
项目介绍：Transform the Page Preview hover into a working editor instance
GitHub 仓库地址： [https://github.com/nothingislost/obsidian-hover-editor](https://github.com/nothingislost/obsidian-hover-editor)

### 添加任何的按钮组件

项目名称：shabegom/buttons
项目介绍：Buttons in Obsidian
GitHub 仓库地址： [https://github.com/shabegom/buttons](https://github.com/shabegom/buttons)

### 浏览历史

项目名称：pjeby/pane-relief
项目介绍：Obsidian plugin for per-pane history, pane movement/navigation hotkeys, and more
GitHub 仓库地址： [https://github.com/pjeby/pane-relief](https://github.com/pjeby/pane-relief)

### 目录 TOC

项目名称：hipstersmoothie/obsidian-plugin-toc
项目介绍：Create a tables of contents for a note.
GitHub 仓库地址： [https://github.com/hipstersmoothie/obsidian-plugin-toc](https://github.com/hipstersmoothie/obsidian-plugin-toc)

### Memo - 类似于 Flomo 的灵感记录插件

项目名称：Quorafind/Obsidian-Memos
项目介绍：A quick capture plugin for Obsidian, all data from your notes.
GitHub 仓库地址： [https://github.com/Quorafind/Obsidian-Memos](https://github.com/Quorafind/Obsidian-Memos)

### 大纲工具

项目名称：vslinko/obsidian-outliner
项目介绍：Work with your lists like in Workflowy or RoamResearch
GitHub 仓库地址： [https://github.com/vslinko/obsidian-outliner](https://github.com/vslinko/obsidian-outliner)

### 更好的模板

项目名称：SilentVoid13/Templater
项目介绍：A template plugin for obsidian
GitHub 仓库地址： [https://github.com/SilentVoid13/Templater](https://github.com/SilentVoid13/Templater)

### 数据视图

项目名称：blacksmithgu/obsidian-dataview
项目介绍：A high-performance data index and query language over Markdown files, for [https://obsidian.md/](https://obsidian.md)
GitHub 仓库地址： [https://github.com/blacksmithgu/obsidian-dataview](https://github.com/blacksmithgu/obsidian-dataview)
正在开发的平替：[blacksmithgu/datacore: Work-in-progress successor to Dataview with a focus on UX and speed.](https://github.com/blacksmithgu/datacore)

### 数据核心

项目名称：blacksmithgu/datacore
介绍：blacksmithgu/datacore: Work-in-progress successor to Dataview with a focus on UX and speed.
仓库地址：[https://github.com/blacksmithgu/datacore](https://github.com/blacksmithgu/datacore)

### 思维导图

项目名称：lynchjames/obsidian-mind-map
项目介绍：An Obsidian plugin for displaying markdown notes as mind maps using Markmap.
GitHub 仓库地址：[https://github.com/lynchjames/obsidian-mind-map](https://github.com/lynchjames/obsidian-mind-map)
