---
status: 尚未完成
---
# API 文档系统

## 说明

一个能够追踪 API 响应对象中的字段在「什么时候」、「什么版本」、「什么提交」里面新增或者发生变更，甚至是提供即将废弃的注解。

在开发的时候期间，Staging 过渡阶段，FAT 特性验收测试阶段，UAT 用户可接受度测试阶段是没办法很好的记录到版本号的，可能得根据提交记录和提交时间来完成，否则开发者得把 API 版本和后端版本剥离，并分别进行记录。不过在我看来，记录版本号、剥离版本号并记录的任意一种方案，甚至是记录提交这类的事情不应该是 API 接口开发者应该做的，应当在 API 定义发生变更之后由 API 文档系统自动变更版本号并且在文档中正确渲染和提示到使用用户。

可能的情况下，该系统甚至能提供一些 DevOps 联动，在必要的情况下可以允许文档系统提示该字段所处的阶段，比如能展示到 Staging 阶段或者是 Production 阶段。

可能的情况下，该系统还应该提示 beta / experiment API 注解高亮说明，并建议「为生产产品开发的开发者：尽可能避免使用正处于 beta / experiment 的 API，这些 API 可能随时发生变更，暂时不稳定，并采用其他替代方案实现」。
