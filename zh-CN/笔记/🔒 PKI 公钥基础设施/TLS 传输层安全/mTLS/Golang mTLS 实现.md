---
tags:
  - 数学/密码学/证书/证书机构/CA
  - 数学/密码学/证书/证书机构
  - 数学/密码学/证书/PKI
  - 命令行/openssl
  - 数学/密码学/证书/TLS/mTLS
  - 开发/语言/Golang
  - 数学/密码学/证书/TLS/SSL
  - 数学/密码学/证书/TLS
status: 尚未完成
---

# Golang mTLS 实现

[Golang 实现创建证书机构（CA）和签发证书](https://shaneutt.com/blog/golang-ca-and-signed-cert-go)

[Golang mTLS 基本实现 - Github Gist](https://gist.github.com/geoah/31340b8155318a3661b1555c191470b5)

[Golang mTLS 完整实现（包含 OpenSSL 签发脚本） - GitHub](https://github.com/nicholasjackson/mtls-go-example)

[Golang mTLS 流程详解](https://kofo.dev/how-to-mtls-in-golang)

[Golang mTLS 服务深入浅出](https://venilnoronha.io/a-step-by-step-guide-to-mtls-in-go)

## Golang 证书实现问题

Does not implement crypto.Signer [](https://github.com/raff/tls-psk/issues/5)
