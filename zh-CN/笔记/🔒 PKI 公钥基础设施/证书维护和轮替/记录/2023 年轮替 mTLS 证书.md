---
tags:
  - 数学/密码学/证书/TLS/mTLS
  - 数学/密码学/证书/TLS
  - 数学/密码学/证书/PKI
  - 命令行/openssl
---
# 2023 年轮替 mTLS 证书

这次维护用了 1Password 帮忙存储和读取 key，不过生成 Private key 的功能尚在 beta 阶段，所以还是先用 openssl 生成。

### 创建今年的文件存储路径

```shell
mkdir -p clients/neko/2023
```

### 生成 Private key 和证书签发请求文件

```shell
openssl genrsa -out clients/neko/2023/client.pem 4096
openssl req -new -key clients/neko/2023/client.pem -out clients/neko/2023/client.csr
```

### 创建证书拓展文件

```shell
cat > clients/neko/2023/client.ext << EOF
basicConstraints = CA:FALSE
nsCertType = client, email
nsComment = "Ayaka Home Users Certificates"
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer
keyUsage = critical, nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = clientAuth, emailProtection
EOF
```

### 证书签发

```shell
op read "op://<secret reference>" > intermediates/home_ca_intermediate_1.pem
openssl x509 -req -days 365 -sha256 -extfile clients/neko/2023/client.ext -in clients/neko/2023/client.csr -CA intermediates/home_ca_intermediate_1.crt -CAkey intermediates/home_ca_intermediate_1.pem -out clients/neko/2023/client.crt -CAcreateserial
rm -rf intermediates/home_ca_intermediate_1.pem
```

### 创建证书链 bundle

``` shell
cat home_ca.crt intermediates/home_ca_intermediate_1.crt > intermediates/home_ca_intermediate_1_bundle.crt
```

### 用证书链 bundle 来验证证书

```shell
openssl verify -CAfile intermediates/home_ca_intermediate_1_bundle.crt clients/neko/2023/client.crt
```

### 验证成功后打包为 .p12 格式的文件方便导入导出和安装

```shell
openssl pkcs12 -export -in clients/neko/2023/client.crt -inkey clients/neko/2023/client.pem -certfile intermediates/home_ca_intermediate_1_bundle.crt -out clients/neko/2023/<EMAIL>.p12
```

成功导入到 KeyStore 里面之后记得删除私钥和证书签发请求文件

```shell
rm -rf clients/neko/2023/client.pem
rm -rf clients/neko/2023/client.csr
```

## 快速脚本

```shell
#!/usr/bin/env bash

read -p "用户名：" USERNAME
read -p "邮箱：" EMAIL
read -p "CA Bundle 名称：" INTERMEDIATE_CA_BUNDLE_NAME
read -p "中间 CA 证书名：" INTERMEDIATE_CA_NAME
read -p "1Password CLI CA Secret Reference：" ONE_PASSWORD_CLI_CA_SECRET_REFERENCE

CURRENT_YEAR=$(date +%Y)

echo "创建用户目录..."
rm -rf clients/$USERNAME/$CURRENT_YEAR
mkdir -p clients/$USERNAME/$CURRENT_YEAR
echo "生成用户私钥..."
openssl genrsa -out clients/$USERNAME/$CURRENT_YEAR/client.pem 4096

echo "生成用户证书签发请求文件..."
cat > clients/$USERNAME/$CURRENT_YEAR/client.cnf << EOF
[req]
prompt = no
distinguished_name = req_distinguished_name

[req_distinguished_name]
C = CN
ST = Shanghai
L = Shanghai
O = Ayaka Home Users
OU = Users
CN = $EMAIL
emailAddress = $EMAIL
EOF

openssl req -new -config clients/$USERNAME/$CURRENT_YEAR/client.cnf -key clients/$USERNAME/$CURRENT_YEAR/client.pem -out clients/$USERNAME/$CURRENT_YEAR/client.csr

echo "创建证书拓展文件..."

cat > clients/$USERNAME/$CURRENT_YEAR/client.ext << EOF
basicConstraints = CA:FALSE
nsCertType = client, email
nsComment = "Ayaka Home Users Certificates"
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer
keyUsage = critical, nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = clientAuth, emailProtection
EOF

echo "证书签发..."
op read $ONE_PASSWORD_CLI_CA_SECRET_REFERENCE > intermediates/$INTERMEDIATE_CA_NAME.pem
openssl x509 -req -days 365 -sha256 -extfile clients/$USERNAME/$CURRENT_YEAR/client.ext -in clients/$USERNAME/$CURRENT_YEAR/client.csr -CA intermediates/$INTERMEDIATE_CA_NAME.crt -CAkey intermediates/$INTERMEDIATE_CA_NAME.pem -out clients/$USERNAME/$CURRENT_YEAR/client.crt -CAcreateserial
rm -rf intermediates/$INTERMEDIATE_CA_NAME.pem

echo "验证证书..."
openssl verify -CAfile intermediates/$INTERMEDIATE_CA_BUNDLE_NAME.crt clients/$USERNAME/$CURRENT_YEAR/client.crt

echo "打包证书..."
openssl pkcs12 -export -in clients/$USERNAME/$CURRENT_YEAR/client.crt -inkey clients/$USERNAME/$CURRENT_YEAR/client.pem -certfile intermediates/$INTERMEDIATE_CA_BUNDLE_NAME.crt -out clients/$USERNAME/$CURRENT_YEAR/$EMAIL.p12
rm -rf clients/$USERNAME/$CURRENT_YEAR/client.pem
rm -rf clients/$USERNAME/$CURRENT_YEAR/client.csr
```
