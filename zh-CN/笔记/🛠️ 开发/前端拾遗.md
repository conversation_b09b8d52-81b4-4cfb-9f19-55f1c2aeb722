---
tags:
  - 开发/前端
  - 索引
---

# 前端拾遗

[前端开发路线图](%E5%89%8D%E7%AB%AF%E5%BC%80%E5%8F%91%E8%B7%AF%E7%BA%BF%E5%9B%BE.md)

- 互联网
  - [互联网是如何工作的](%E4%BA%92%E8%81%94%E7%BD%91%E6%98%AF%E5%A6%82%E4%BD%95%E5%B7%A5%E4%BD%9C%E7%9A%84.md)
  - 什么是 HTTP？
  - 浏览器以及其运行机制？
  - DNS 以及其运行机制？
  - 什么是域名？
  - 什么是网站托管？
- HTML
  - 惯例和最佳实践
  - 可访问性
- CSS
  - 制作布局
    - 盒模型
    - 网格布局
    - 弹性布局
  - 媒体查询

- JavaScript
  - 学习 DOM 操作
  - 模块化 JavaScript
  - 概念
    - 变量提升
    - 作用域原型
- 版本控制
  - GIt 命令行操作
- Web 安全知识
  - HTTPS
  - 内容安全策略 CSP
  - 跨域资源共享
  - OWASP 安全风险
- 格式化工具
  - ESLint Vue3 开发环境配置
- 打包工具
  - Webpack
  - Vite
- 现代 CSS
  - CSS in JS
  - Tailwind CSS

- 测试，集成
  - Jest
  - ……
- 渐进式网页应用程序（PWA）
  - ……
- 服务端渲染 SSR，SSG
- GraphQL
- 桌面应用开发
  - Electron
  - Uni App
- Web Assembly
- ……

[什么是设计规范](%E4%BB%80%E4%B9%88%E6%98%AF%E8%AE%BE%E8%AE%A1%E8%A7%84%E8%8C%83.md)
