# 物种关系

##### 文档版本

| 编辑者 | 版本 | 变更日期 | 变更说明 |
| ----- | --- | ------- | ------- |
| Neko | v1.0.0 | 2021-12-03 | 创建 |

### 文档兼容性

| 主体 | 版本号 | 文档地址（如果有） |
| -- | -- | -- |
| NebulaGraph | v2.6.1 | [https://docs.nebula-graph.com.cn/2.6.1/](https://docs.nebula-graph.com.cn/2.6.1/) |

## 语句记录

```sql
CREATE SPACE IF NOT EXISTS ng_playground1(vid_type=INT64);
```

```sql
CREATE TAG `user` (
  user_id int NOT NULL DEFAULT 0 COMMENT "用户 ID",
  username string NOT NULL DEFAULT "" COMMENT "用户名",
  create_time timestamp NOT NULL DEFAULT 0 COMMENT "创建时间",
  modified_time timestamp NOT NULL DEFAULT 0 COMMENT "变更时间"
) COMMENT = "用户"
```

```sql
CREATE TAG INDEX IF NOT EXISTS user_user_id ON user(user_id);
```

```sql
CREATE TAG user_info (
  intro string NOT NULL DEFAULT "" COMMENT "介绍",
  gender string NOT NULL DEFAULT "" COMMENT "性别",
  species string NOT NULL DEFAULT "" COMMENT "物种",
  create_time timestamp NOT NULL DEFAULT 0 COMMENT "创建时间",
  modified_time timestamp NOT NULL DEFAULT 0 COMMENT "变更时间"
) COMMENT = "用户信息"
```

```sql
CREATE TAG INDEX IF NOT EXISTS user_info_user_id ON user_info;
```

```sql
INSERT VERTEX user (user_id, username, create_time, modified_time) VALUES 2:(2, "Neko2", now(), now());
INSERT VERTEX user (user_id, username, create_time, modified_time) VALUES 1:(1, "Neko1", now(), now());
INSERT VERTEX user_info (intro, gender, species, create_time, modified_time) VALUES 3:("喵呜1", "公", "猫咪", now(), now());
INSERT VERTEX user_info (intro, gender, species, create_time, modified_time) VALUES 4:("喵呜2", "母", "猫咪", now(), now());
```

```sql
CREATE EDGE user_intro (
	user_id int NULL DEFAULT 0 COMMENT "用户 ID"
)  COMMENT = "用户信息"
```
