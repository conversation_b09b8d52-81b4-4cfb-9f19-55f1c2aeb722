# 用户和评论

##### 文档版本

| 编辑者 | 版本 | 变更日期 | 变更说明 |
| ----- | --- | ------- | ------- |
| Neko | v1.0.0 | 2021-12-03 | 创建 |

### 文档兼容性

| 主体 | 版本号 | 文档地址（如果有） |
| -- | -- | -- |
| NebulaGraph | v2.6.1 | [https://docs.nebula-graph.com.cn/2.6.1/](https://docs.nebula-graph.com.cn/2.6.1/) |

## 结构

```sql
-- 创建空间
CREATE SPACE IF NOT EXISTS users_and_comments(vid_type=INT64);

CREATE TAG `user` (
  id int NOT NULL DEFAULT 0 COMMENT "用户 ID",
  username string NOT NULL DEFAULT "" COMMENT "用户名"
) COMMENT = "用户";

CREATE TAG INDEX IF NOT EXISTS user_id ON user(id);

CREATE TAG `sound` (
  id int NOT NULL DEFAULT 0 COMMENT "音频 ID",
  name string NOT NULL DEFAULT "" COMMENT "音频名称",
  user_id int NOT NULL DEFAULT 0 COMMENT "用户 ID",
  comment int NOT NULL DEFAULT 0 COMMENT "评论数",
  sub_comment int NOT NULL DEFAULT 0 COMMENT "子评论数"
) COMMENT = "音频";

CREATE TAG INDEX IF NOT EXISTS sound_id ON sound(id);
CREATE TAG INDEX IF NOT EXISTS sound_user_id ON sound(user_id);

CREATE TAG `comment` (
  id int NOT NULL DEFAULT 0 COMMENT "评论 ID",
  content string NOT NULL DEFAULT "" COMMENT "评论内容",
  user_id int NOT NULL DEFAULT 0 COMMENT "用户 ID",
  sound_id int NOT NULL DEFAULT 0 COMMENT "音频 ID",
  sub_comment int NOT NULL DEFAULT 0 COMMENT "子评论数",
  likes int NOT NULL DEFAULT 0 COMMENT "点赞数",
  dislikes int NOT NULL DEFAULT 0 COMMENT "点踩数"
) COMMENT = "评论";

CREATE TAG INDEX IF NOT EXISTS comment_id ON comment(id);
CREATE TAG INDEX IF NOT EXISTS comment_user_id ON comment(user_id);
CREATE TAG INDEX IF NOT EXISTS comment_sound_id ON comment(sound_id);

CREATE TAG `sub_comment` (
  id int NOT NULL DEFAULT 0 COMMENT "子评论 ID",
  content string NOT NULL DEFAULT "" COMMENT "评论内容",
  user_id int NOT NULL DEFAULT 0 COMMENT "用户 ID",
  sound_id int NOT NULL DEFAULT 0 COMMENT "音频 ID",
  comment_id int NOT NULL DEFAULT 0 COMMENT "父评论 ID",
  likes int NOT NULL DEFAULT 0 COMMENT "点赞数",
  dislikes int NOT NULL DEFAULT 0 COMMENT "点踩数"
) COMMENT = "子评论";

CREATE TAG INDEX IF NOT EXISTS sub_comment_id ON sub_comment(id);
CREATE TAG INDEX IF NOT EXISTS sub_comment_user_id ON sub_comment(user_id);
CREATE TAG INDEX IF NOT EXISTS sub_comment_sound_id ON sub_comment(sound_id);
CREATE TAG INDEX IF NOT EXISTS sub_comment_comment_id ON sub_comment(comment_id);

CREATE EDGE commented (
  type int NULL DEFAULT 0 COMMENT "0 为父评论，1 为子评论"
) COMMENT = "评论";

CREATE EDGE INDEX IF NOT EXISTS commented_type ON commented(type);

CREATE EDGE uploaded_audio () COMMENT = "上传音频";

CREATE EDGE INDEX IF NOT EXISTS uploaded_audio ON uploaded_audio();

CREATE EDGE liked () COMMENT = "点赞过";

CREATE EDGE INDEX IF NOT EXISTS liked ON liked();

CREATE EDGE disliked () COMMENT = "点踩过";

CREATE EDGE INDEX IF NOT EXISTS disliked ON disliked();

CREATE EDGE sub_commented () COMMENT = "子评论";

CREATE EDGE INDEX IF NOT EXISTS sub_commented ON sub_commented();

CREATE EDGE sound_comment () COMMENT = "音频评论";

CREATE EDGE INDEX IF NOT EXISTS sound_comment ON sound_comment();

```

## 数据

```sql
INSERT VERTEX `user` (id, username) VALUES 1:(1, "UP 主1");
INSERT VERTEX `user` (id, username) VALUES 2:(2, "UP 主2");
INSERT VERTEX `user` (id, username) VALUES 3:(3, "UP 主3");
INSERT VERTEX `user` (id, username) VALUES 4:(4, "UP 主4");
INSERT VERTEX `user` (id, username) VALUES 5:(5, "UP 主5");
INSERT VERTEX `user` (id, username) VALUES 6:(6, "用户1");
INSERT VERTEX `user` (id, username) VALUES 7:(7, "用户2");
INSERT VERTEX `user` (id, username) VALUES 8:(8, "用户3");
INSERT VERTEX `user` (id, username) VALUES 9:(9, "用户4");
INSERT VERTEX `user` (id, username) VALUES 10:(10, "用户5");

INSERT VERTEX sound (id, name, user_id, comment, sub_comment) VALUES 11:(1, "音频1 - 无评论", 1, 0, 0);
INSERT EDGE uploaded_audio () VALUES 1->11;
INSERT VERTEX sound (id, name, user_id, comment, sub_comment) VALUES 12:(2, "音频2 - 有父评论", 2, 0, 0);
INSERT EDGE uploaded_audio () VALUES 2->12:();
INSERT VERTEX sound (id, name, user_id, comment, sub_comment) VALUES 13:(3, "音频3 - 有父评论和子评论", 3, 0, 0);
INSERT EDGE uploaded_audio () VALUES 3->13:();
INSERT VERTEX sound (id, name, user_id, comment, sub_comment) VALUES 14:(4, "音频4 - 有父评论点赞", 4, 0, 0);
INSERT EDGE uploaded_audio () VALUES 4->14:();
INSERT VERTEX sound (id, name, user_id, comment, sub_comment) VALUES 15:(5, "音频5 - 有子评论点赞", 4, 0, 0);
INSERT EDGE uploaded_audio () VALUES 4->15:();
INSERT VERTEX sound (id, name, user_id, comment, sub_comment) VALUES 16:(6, "音频6 - 有父评论点踩", 5, 0, 0);
INSERT EDGE uploaded_audio () VALUES 5->16:();
INSERT VERTEX sound (id, name, user_id, comment, sub_comment) VALUES 17:(8, "音频7 - 有子评论点踩", 5, 0, 0);
INSERT EDGE uploaded_audio () VALUES 5->17:();

INSERT VERTEX comment (id, content, user_id, sound_id, sub_comment, likes, dislikes) VALUES 18:(1, "评论1 - 父评论", 6, 2, 0, 0, 0);
INSERT EDGE commented (type) VALUES 6->18:(0);
INSERT EDGE sound_comment () VALUES 12->18:();

INSERT VERTEX comment (id, content, user_id, sound_id, sub_comment, likes, dislikes) VALUES 19:(2, "子评论1 - 子评论", 7, 3, 0, 0, 0);
INSERT EDGE commented (type) VALUES 7->19:(1);
INSERT EDGE sub_commented () VALUES 18->19:();
INSERT EDGE sound_comment () VALUES 13->19:();

INSERT VERTEX comment (id, content, user_id, sound_id, sub_comment, likes, dislikes) VALUES 20:(3, "评论3 - 父评论有点赞", 6, 4, 0, 0, 0);
INSERT EDGE commented (type) VALUES 6->20:(0);
INSERT EDGE liked () VALUES 8->20:();
INSERT EDGE sound_comment () VALUES 14->20:();

INSERT VERTEX comment (id, content, user_id, sound_id, sub_comment, likes, dislikes) VALUES 21:(4, "评论4 - 子评论有点赞", 7, 4, 0, 0, 0);
INSERT EDGE commented (type) VALUES 7->21:(0);
INSERT EDGE sound_comment () VALUES 14->21:();

INSERT VERTEX comment (id, content, user_id, sound_id, sub_comment, likes, dislikes) VALUES 22:(5, "子评论2 - 子评论有点赞", 7, 4, 0, 0, 0);
INSERT EDGE commented (type) VALUES 7->22:(1);
INSERT EDGE sub_commented () VALUES 21->22:();
INSERT EDGE liked () VALUES 9->22:();
INSERT EDGE sound_comment () VALUES 14->22:();

INSERT VERTEX comment (id, content, user_id, sound_id, sub_comment, likes, dislikes) VALUES 23:(6, "评论3 - 父评论有点踩", 6, 4, 0, 0, 0);
INSERT EDGE commented (type) VALUES 6->22:(0);
INSERT EDGE disliked () VALUES 9->22:();
INSERT EDGE sound_comment () VALUES 14->23:();

INSERT VERTEX comment (id, content, user_id, sound_id, sub_comment, likes, dislikes) VALUES 24:(7, "评论4 - 子评论有点踩", 7, 5, 0, 0, 0);
INSERT EDGE commented (type) VALUES 7->21:(0);
INSERT EDGE sound_comment () VALUES 15->24:();

INSERT VERTEX comment (id, content, user_id, sound_id, sub_comment, likes, dislikes) VALUES 25:(8, "子评论3 - 子评论有点踩", 7, 5, 0, 0, 0);
INSERT EDGE commented (type) VALUES 7->25:(1);
INSERT EDGE sub_commented () VALUES 24->25:();
INSERT EDGE disliked () VALUES 10->25:();
INSERT EDGE sound_comment () VALUES 15->25:();
```
