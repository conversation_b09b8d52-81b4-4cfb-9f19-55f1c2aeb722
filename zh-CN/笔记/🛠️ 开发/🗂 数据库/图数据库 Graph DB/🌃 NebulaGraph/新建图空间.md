# 新建图空间

##### 文档版本

| 编辑者 | 版本 | 变更日期 | 变更说明 |
| ----- | --- | ------- | ------- |
| Neko | v1.0.0 | 2021-12-01 | |

### 文档兼容性

| 主体 | 版本号 | 文档地址（如果有） |
| -- | -- | -- |
| NebulaGraph | v2.6.1 | [https://docs.nebula-graph.com.cn/2.6.1/](https://docs.nebula-graph.com.cn/2.6.1/) |

## 说明

空间（Space）类似于 SQL 的数据库

## 语法[^1]

1. 中（方）括号表示可选，实际语句中不要出现中（方）括号
2. 尖括号表示替换，需要把你实际需要填写的值替换到尖括号所在的位置上

```sql
CREATE SPACE [IF NOT EXISTS] <graph_space_name> ( [partition_num = <partition_number>,] [replica_factor = <replica_number>,] vid_type = {FIXED_STRING(<N>) | INT[64]} ) [COMMENT = '<comment>'];
```

### 参数说明

|参数|说明|
|:---|:---|
|`IF NOT EXISTS`|检测待创建的图空间是否存在，只有不存在时，才会创建图空间。仅检测图空间的名称，不会检测具体属性。|
|`<graph_space_name>`|在Nebula Graph实例中唯一标识一个图空间。图空间名称由大小写英文字母、数字或下划线组成，区分大写小，且不可使用关键字和保留字。|
|`partition_num`|指定图空间的分片数量。建议设置为5倍的集群硬盘数量。例如集群中有3个硬盘，建议设置15个分片。默认值为100。|
|`replica_factor`|指定每个分片的副本数量。建议在生产环境中设置为3，在测试环境中设置为1。由于需要基于多数表决，副本数量必须是**奇数**。默认值为1。|
|`vid_type`|必选参数。指定点ID的数据类型。可选值为`FIXED_STRING(<N>)`和`INT64`。`INT`等同于`INT64`。`FIXED_STRING(<N>)`表示数据类型为字符串，最大长度为`N`，超出长度会报错；`INT64`表示数据类型为整数。|
|`ON <group_name>`|指定图空间所属的Group。详情请参见 Group & Zone。|
|`COMMENT`|图空间的描述。最大为256字节。默认无描述。|

## 创建一个 vid_type 类型为 INT64（64 位整型）的空间

```sql
CREATE SPACE IF NOT EXISTS space_name(vid_type=INT64);
```

## 创建一个 vid_type 类型为 FIXED_STRING(20)（长度为20的固定字符串）的空间

```sql
CREATE SPACE IF NOT EXISTS space_name(vid_type=FIXED_STRING(20));
```

[^1]: [CREATE SPACE（创建空间） - Nebula Graph Database 手册](https://docs.nebula-graph.com.cn/2.6.1/3.ngql-guide/9.space-statements/1.create-space/)
