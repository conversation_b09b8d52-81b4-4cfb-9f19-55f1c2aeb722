# 新建标签

##### 文档版本

| 编辑者 | 版本 | 变更日期 | 变更说明 |
| ----- | --- | ------- | ------- |
| Neko | v1.0.0 | 2021-12-01 | |

### 文档兼容性

| 主体 | 版本号 | 文档地址（如果有） |
| -- | -- | -- |
| NebulaGraph | v2.6.1 | [https://docs.nebula-graph.com.cn/2.6.1/](https://docs.nebula-graph.com.cn/2.6.1/) |

## 说明

标签（Tag）类似于 SQL 的表

## 语法

```sql
CREATE TAG [IF NOT EXISTS] <tag_name> (
  <prop_name> <data_type> [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>'] [{, <prop_name> <data_type> [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>']} ...] ) [TTL_DURATION = <ttl_duration>] [TTL_COL = <prop_name>] [COMMENT = '<comment>'];
```

## 示例

比如创建一个用户标签

```sql
CREATE TAG `user` (
  user_id int NOT NULL DEFAULT 0 COMMENT "用户 ID",
  username string NOT NULL DEFAULT "" COMMENT "用户名",
  create_time timestamp NOT NULL DEFAULT 0 COMMENT "创建时间",
  modified_time timestamp NOT NULL DEFAULT 0 COMMENT "变更时间"
) COMMENT = "用户"
```
