---
status: 尚未完成
---
# 系统设计

## 参考资料

### 架构相关

[使用 Nginx 将客户端证书信息转发到上游 - Wordpress](https://clairekeum.wordpress.com/2018/12/05/passing-client-cert-through-nginx-to-the-backend/)

[Nginx 反向代理配置传递客户端证书给后端 - ServerFault](https://serverfault.com/questions/788895/nginx-reverse-proxy-pass-through-client-certificate)

[在 Nginx 中运行 Lua 代码（使用 lua 代码来完成证书解析和处理） - StackOverflow](https://stackoverflow.com/questions/********/running-lua-in-nginx-config)

[SSO 逻辑设计](https://stackoverflow.com/questions/********/automatic-cookie-single-sign-on-on-multiple-domains-like-google)

### 用户系统

[大佬指路：如何设计用户验证和管理系统 2021 - Google Cloud Platform](https://cloud.google.com/blog/products/identity-security/account-authentication-and-password-management-best-practices)
