[千万级延时任务队列如何实现，看美图开源的-LMSTFY - 知乎](https://zhuanlan.zhihu.com/p/94082947)

[基于 Golang 实现的延迟队列的设计与分析（一） - 熊喵君的博客 | PANDAYCHEN](https://pandaychen.github.io/2020/10/02/A-DELAY-QUEUE-ANALYSIS/)

[mix-basic/delayer: 🌶️ 基于 Redis 的延迟队列中间件，采用 Golang 开发，支持 PHP、Golang 等多种语言客户端](https://github.com/mix-basic/delayer)

[goware/disque: Golang client for Disque, the Persistent Distributed Job Priority Queue](https://github.com/goware/disque)

[EverythingMe/go-disque: Go client for Disque](https://github.com/EverythingMe/go-disque)

[实战讲解高并发和秒杀抢购系统设计 - 腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1346263)

[高并发系统的设计及秒杀实践 - 腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1355485?from=article.detail.1346263)
