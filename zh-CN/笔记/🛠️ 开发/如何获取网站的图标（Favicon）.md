---
tags:
  - 软件/浏览器/Chrome
  - 软件/浏览器/Edge
---
# 如何获取网站的图标（Favicon）

## 用浏览器自带的功能获取

::: code-group

``` [Microsoft Edge]
edge://favicon/<带协议前缀的完整域名和路径>
```

``` [Google Chrome]
chrome://favicon/<带协议前缀的完整域名和路径>
```

:::

这个链接没办法直接通过浏览器打开，你需要手动复制粘贴到浏览器中尝试：

::: code-group

``` [Microsoft Edge]
edge://favicon/https://nolebase.ayaka.io
```

``` [Google Chrome]
chrome://favicon/https://nolebase.ayaka.io
```

:::
## 参考资料

- [Grab favicon.ico using Google Chrome dev tools - Super User](https://superuser.com/a/1443114)