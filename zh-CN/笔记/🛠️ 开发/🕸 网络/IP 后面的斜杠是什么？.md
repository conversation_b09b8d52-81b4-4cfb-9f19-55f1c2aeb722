---
tags:
  - 计算机/网络
  - 计算机/网络/CIDR
  - 计算机/网络
  - 知识领域/计算机网络
---
# IP 后面的斜杠是什么？

## 举个栗子

有斜杠的例子：

1. `xx.xx.xx.2/24`
2. `xx.xx.xx.0/24`

通俗一点就是，斜杠后面的数字就表示子网掩码，数字具体代表 32 位子网掩码（二进制形式）中前面的 `1` 的个数。
而且前面的 IP 地址 也不一定是一个 IP 地址，也可能是一个网络号（末位是0）。
通过后面数字可以将前面的网段进一步细划分成具体的子网。

比如：

1. `xx.xx.xx.2/24`   ——> 表示一个 IP 地址 `xx.xx.xx.2`，`24` 表示了这个 IP 地址所对应的子网掩码。
2. `xx.xx.xx.0/24`   ——> 表示一个网段，并且 `24` 体现了当前具体的「子网掩码」。

其实这个就是用特殊的形式表示的一个网段，或者说子网，这样的特殊表达方式叫[无类别域间路由 CIDR](https://zh.wikipedia.org/wiki/%E6%97%A0%E7%B1%BB%E5%88%AB%E5%9F%9F%E9%97%B4%E8%B7%AF%E7%94%B1)

## 详细说明

我们知道确定一个子网需要知道主机地址和子网掩码，但用 CIDR 的形式，可以简单得到两个数值。
举例说吧：

1. `***********/24` 就表示，这个网段的IP地址从 `***********` 开始，到 `*************` 结束（`***********` 和 `*************` 有特殊含义，不能用作 IP 地址）；子网掩码是 `*************`。

上面的子网掩码怎么来的呢？其实关键就在 **24** 上。

我们知道 IP 地址是四个十进制数组成的，相当于 32 位二进制。用 无类别域间路由 CIDR 表示形式，后一个数字将这 32 位进行了间隔（以 `24` 为例）：前 24 位用 `1` 表示，后面 8 位用 `0` 表示，得到一个二进制数： `11111111 11111111 11111111 00000000`，将其转化为十进制，就是：`*************` 了。

2. `***********/24` 表示网段是 `***********`，子网掩码是 24 位，子网掩码为：`*************`，用二进制表示为：`11111111 11111111 11111111 00000000`

这里为什么是 `24` 呢，就是因为子网 掩码里面的前面连续的 `1` 的个数为 24 个，一定要连续的才行。

3. `***********/28`表示的意思是网段是 `***********`，子网掩码为：`***************`，用二进制表示为：`11111111 11111111 11111111 11110000`。

这时候你也许就疑惑了，就是 24 和 28 两个字不一样，为什么网段是一样的呢？
24 位说明网络位是 24 位，那么主机位就是 32 - 24 = 8 位了，则子网的 IP 个数是 254 个，即是从 `00000001` 到 `11111110`。
28 位说明网络位是 28 位，那么主机位 4 位，则子网的 IP 个数是 14 个，即是从 `00000001` 到 `00001110`

## 总结

CIDR 的另一个好处就是可以进行**前缀路由聚合**。例如，16个原来的 C 类（`/24`）网络现在可以聚合在一起，对外显示了一个 `/20` 的网络了（如果这些网络的的地址前 `20` 位都相同）。两个对齐的 `/20` 网络又可进一步聚合为 `/19`，依此类推。这有效地减少了要对外显示的网络数，防止了路由表爆炸，也遏制了互联网进一步扩大。

## CIDR 各个不同数字对应的范围参考表格

|    IP/CIDR     | Δ 与最后一个地址的差值 |      掩码       |  主机数 (*)   |    类别     |          备注           |
| :------------: | :--------------------: | :-------------: | :-----------: | :---------: | :---------------------: |
| a.b.c.d**/32** |        +0.0.0.0        | *************** |       1       |   1/256 C   |                         |
| a.b.c.d**/31** |        +*******        | *************** |       2       |   1/128 C   | d = 0 ... (2n) ... 254  |
| a.b.c.d**/30** |        +*******        | *************** |       4       |   1/64 C    | d = 0 ... (4n) ... 252  |
| a.b.c.d**/29** |        +*******        | *************** |       8       |   1/32 C    | d = 0 ... (8n) ... 248  |
| a.b.c.d**/28** |       +*******5        | *************** |      16       |   1/16 C    | d = 0 ... (16n) ... 240 |
| a.b.c.d**/27** |       +*******1        | *************** |      32       |    1/8 C    | d = 0 ... (32n) ... 224 |
| a.b.c.d**/26** |       +********        | 255.255.255.192 |      64       |    1/4 C    |   d = 0, 64, 128, 192   |
| a.b.c.d**/25** |       +*******27       | 255.255.255.128 |      128      |    1/2 C    |       d = 0, 128        |
| a.b.c.0**/24** |       +0.0.0.255       | *************00 |      256      |     1 C     |                         |
| a.b.c.0**/23** |       +0.0.1.255       | 255.255.254.000 |      512      |     2 C     | c = 0 ... (2n) ... 254  |
| a.b.c.0**/22** |       +0.0.3.255       | 255.255.252.000 |     1,024     |     4 C     | c = 0 ... (4n) ... 252  |
| a.b.c.0**/21** |       +0.0.7.255       | 255.255.248.000 |     2,048     |     8 C     | c = 0 ... (8n) ... 248  |
| a.b.c.0**/20** |      +0.0.15.255       | 255.255.240.000 |     4,096     |    16 C     | c = 0 ... (16n) ... 240 |
| a.b.c.0**/19** |      +0.0.31.255       | 255.255.224.000 |     8,192     |    32 C     | c = 0 ... (32n) ... 224 |
| a.b.c.0**/18** |      +0.0.63.255       | 255.255.192.000 |    16,384     |    64 C     |   c = 0, 64, 128, 192   |
| a.b.c.0**/17** |      +0.0.127.255      | 255.255.128.000 |    32,768     |    128 C    |       c = 0, 128        |
| a.b.0.0**/16** |      +0.0.255.255      | 255.255.000.000 |    65,536     | 256 C = 1 B |                         |
| a.b.0.0**/15** |      +0.1.255.255      | 255.254.000.000 |    131,072    |     2 B     | b = 0 ... (2n) ... 254  |
| a.b.0.0**/14** |      +0.3.255.255      | 255.252.000.000 |    262,144    |     4 B     | b = 0 ... (4n) ... 252  |
| a.b.0.0**/13** |      +0.7.255.255      | 255.248.000.000 |    524,288    |     8 B     | b = 0 ... (8n) ... 248  |
| a.b.0.0**/12** |     +0.15.255.255      | 255.240.000.000 |   1,048,576   |    16 B     | b = 0 ... (16n) ... 240 |
| a.b.0.0**/11** |     +0.31.255.255      | 255.224.000.000 |   2,097,152   |    32 B     | b = 0 ... (32n) ... 224 |
| a.b.0.0**/10** |     +0.63.255.255      | 255.192.000.000 |   4,194,304   |    64 B     |   b = 0, 64, 128, 192   |
| a.b.0.0**/9**  |     +0.127.255.255     | 255.128.000.000 |   8,388,608   |    128 B    |       b = 0, 128        |
| a.0.0.0**/8**  |     +0.255.255.255     | 255.000.000.000 |  16,777,216   | 256 B = 1 A |                         |
| a.0.0.0**/7**  |     +1.255.255.255     | 254.000.000.000 |  33,554,432   |     2 A     | a = 0 ... (2n) ... 254  |
| a.0.0.0**/6**  |     +3.255.255.255     | 252.000.000.000 |  67,108,864   |     4 A     | a = 0 ... (4n) ... 252  |
| a.0.0.0**/5**  |     +7.255.255.255     | 248.000.000.000 |  134,217,728  |     8 A     | a = 0 ... (8n) ... 248  |
| a.0.0.0**/4**  |    +15.255.255.255     | 240.000.000.000 |  268,435,456  |    16 A     | a = 0 ... (16n) ... 240 |
| a.0.0.0**/3**  |    +31.255.255.255     | 224.000.000.000 |  536,870,912  |    32 A     | a = 0 ... (32n) ... 224 |
| a.0.0.0**/2**  |    +63.255.255.255     | 192.000.000.000 | 1,073,741,824 |    64 A     |   a = 0, 64, 128, 192   |
| a.0.0.0**/1**  |    +127.255.255.255    | 128.000.000.000 | 2,147,483,648 |    128 A    |       a = 0, 128        |
| 0.0.0.0**/0**  |    +***************    | 000.000.000.000 | 4,294,967,296 |    256 A    |                         |
