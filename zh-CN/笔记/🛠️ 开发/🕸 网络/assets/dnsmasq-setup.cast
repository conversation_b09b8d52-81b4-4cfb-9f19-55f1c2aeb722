{"version": 2, "width": 272, "height": 34, "timestamp": 1634878113, "env": {"SHELL": "/bin/zsh", "TERM": "xterm-256color"}}
[1.663596, "o", "s"]
[1.857493, "o", "s"]
[2.027698, "o", "h"]
[2.182995, "o", " "]
[2.763364, "o", "d"]
[2.882524, "o", "a"]
[3.047733, "o", "s"]
[3.138514, "o", "h"]
[3.271432, "o", " "]
[3.332582, "o", "\u001b]0;~\u0007"]
[3.336148, "o", "\r\n"]
[3.336242, "o", "\u001b]2;yuna@Ayaka-MBP:~\u0007\u001b]1;~\u0007"]
[3.400599, "o", "\u001b[0m\u001b[27m\u001b[24m\u001b[J\u001b[34m~\u001b[39m\r\n\r\u001b[35m❯\u001b[39m \u001b[K"]
[3.400722, "o", "\u001b[?1h\u001b="]
[3.400792, "o", "\u001b[?2004h"]
[3.402499, "o", "s"]
[3.40286, "o", "\bss"]
[3.4032, "o", "h"]
[3.403909, "o", " "]
[3.404243, "o", "d"]
[3.404565, "o", "a"]
[3.404922, "o", "s"]
[3.405198, "o", "h"]
[3.410378, "o", "\u001b[8D\u001b[32ms\u001b[32ms\u001b[32mh\u001b[39m\u001b[5C \u001b[90m-vvv\u001b[39m\b\b\b\b"]
[3.42274, "o", "\u001b[39m0\u001b[39m \u001b[39m \u001b[39m \b\b\b"]
[3.554069, "o", "1"]
[4.041753, "o", "\b \b"]
[4.210176, "o", "\b\u001b[90m-\u001b[90mvvv\u001b[39m\b\b\b\b"]
[4.355181, "o", "\b\u001b[90m0\u001b[90m1\u001b[39m\u001b[39m \u001b[39m \u001b[39m \b\b\b\b\b"]
[4.497682, "o", "\u001b[39m0"]
[4.6046, "o", "\u001b[39m1"]
[4.725169, "o", "\u001b[?1l\u001b>"]
[4.725215, "o", "\u001b[?2004l\r\r\n"]
[4.726373, "o", "\u001b]0;yuna: ssh dash01\u0007"]
[4.726515, "o", "\u001b]2;ssh dash01\u0007\u001b]1;dash01\u0007"]
[5.658561, "o", "Activate the web console with: systemctl enable --now cockpit.socket\r\n\r\nLast login: Fri Oct 22 04:43:58 2021 from 114.88.135.58\r\r\n"]
[5.778225, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[11.090708, "o", "# 调整 systemd-resolved 服务，防止 systemd-resolved 和 dnsmasq 占用同一端口"]
[11.697132, "o", "\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[12.624285, "o", "s"]
[12.771759, "o", "u"]
[12.829171, "o", "d"]
[12.940878, "o", "o"]
[13.025626, "o", " "]
[13.120369, "o", "v"]
[13.212147, "o", "i"]
[13.317407, "o", "m"]
[13.454073, "o", " "]
[13.919122, "o", "/"]
[14.061554, "o", "e"]
[14.180529, "o", "t"]
[14.307122, "o", "c"]
[14.44196, "o", "/"]
[14.749287, "o", "s"]
[14.866369, "o", "y"]
[14.945225, "o", "s"]
[15.029558, "o", "t"]
[15.282576, "o", "\u0007em"]
[16.264146, "o", "d"]
[16.484905, "o", "/"]
[16.959477, "o", "r"]
[17.004625, "o", "e"]
[17.228188, "o", "solved.conf "]
[17.62961, "o", "\r\n"]
[17.687166, "o", "\u001b[?2004h\u001b[?1049h\u001b[22;0;0t\u001b[?1h\u001b=\u001b[?2004h\u001b[1;34r\u001b[?12h\u001b[?12l\u001b[27m\u001b[23m\u001b[29m\u001b[m\u001b[H\u001b[2J\u001b[?25l\u001b[34;1H\"/etc/systemd/resolved.conf\" 23L, 630C"]
[17.688733, "o", "\u001b[2;1H▽\u001b[6n\u001b[2;1H  \u001b[1;1H\u001b[>c\u001b]10;?\u0007\u001b]11;?\u0007\u001b[1;1H\u001b[34m#  This file is part of systemd.\r\n#\r\n#  systemd is free software; you can redistribute it and/or modify it\r\n#  under the terms of the GNU Lesser General Public License as published by\r\n#  the Free Software Foundation; either version 2.1 of the License, or\r\n#  (at your option) any later version.\r\n#\r\n# Entries in this file show the compile time defaults.\r\n# You can change settings by editing this file.\r\n# Defaults can be restored by simply deleting this file.\r\n#\r\n# See resolved.conf(5) for details\u001b[m\r\n\r\n[Resolve]\r\n\u001b[34m#DNS=\r\n#FallbackDNS=\r\n#Domains=\r\n#LLMNR=yes\r\n#MulticastDNS=yes\r\n#DNSSEC=allow-downgrade\r\n#DNSOverTLS=no\r\n#Cache=yes\r\n#DNSStubListener=no\u001b[m\r\n\u001b[94m~                                                                                                                                                                                                                                                                               \u001b[25;1H~                   "]
[17.68882, "o", "                                                                                                                                                                                                                                                            \u001b[26;1H~                                                                                                                                                                                                                                                                               \u001b[27;1H~                                                                                                                                                                                                                                                                               \u001b[28;1H~                                                                                                                                                                                                              "]
[17.688921, "o", "                                            "]
[17.700005, "o", "                     \u001b[29;1H~                                                                                                                                                                                                                                                                               \u001b[30;1H~                                                                                                                                                                                                                                                                               \u001b[31;1H~                                                                                                                                                                                                                                                                               \u001b[32;1H~                                                                                                                                                              "]
[17.700259, "o", "                                                                                                                 \u001b[33;1H~                                                                                                                                                                                                                                                                               \u001b[m\u001b[34;255H23,1\u001b[10CAll\u001b[23;1H\u001b[?25h"]
[17.775133, "o", "\u001b[?12$p\u001b[27m\u001b[23m\u001b[29m\u001b[m\u001b[H\u001b[2J\u001b[?25l\u001b[1;1H\u001b[96m#  This file is part of systemd.\r\n#\r\n#  systemd is free software; you can redistribute it and/or modify it\r\n#  under the terms of the GNU Lesser General Public License as published by\r\n#  the Free Software Foundation; either version 2.1 of the License, or\r\n#  (at your option) any later version.\r\n#\r\n# Entries in this file show the compile time defaults.\r\n# You can change settings by editing this file.\r\n# Defaults can be restored by simply deleting this file.\r\n#\r\n# See resolved.conf(5) for details\u001b[m\r\n\r\n[Resolve]\r\n\u001b[96m#DNS=\r\n#FallbackDNS=\r\n#Domains=\r\n#LLMNR=yes\r\n#MulticastDNS=yes\r\n#DNSSEC=allow-downgrade\r\n#DNSOverTLS=no\r\n#Cache=yes\r\n#DNSStubListener=no\u001b[m\r\n\u001b[94m~                                                                                                                                                                                                                                                                               \u001b[25;1H~                          "]
[17.775319, "o", "                                                                                                                                                                                                                                                     \u001b[26;1H~                                                                                                                                                                                                                                                                               \u001b[27;1H~                                                                                                                                                                                                                                                                               \u001b[28;1H~                                                                                                                                                                                                                     "]
[17.775451, "o", "                                                          \u001b[29;1H~                                                                                                                                                                                                                                                                               \u001b[30;1H~                                                                                                                                                                                                                                                                               \u001b[31;1H~                                                                                                                                                                                                                                                                               \u001b[32;1H~                                                                                                                         "]
[17.775656, "o", "                                                                                                                                                      \u001b[33;1H~                                                                                                                                                                                                                                                                               \u001b[m\u001b[34;255H23,1\u001b[10CAll\r\"/etc/systemd/resolved.conf\" 23L, 630C\u001b[23;1H\u001b[?25h"]
[18.649179, "o", "\u001b[?25l\u001b[34;245Hi\u001b[23;1H\u001b[34;245H \u001b[23;1H\u001b[34;1H\u001b[1m-- INSERT --\u001b[m\u001b[34;13H\u001b[K\u001b[34;255H23,1\u001b[10CAll\u001b[23;1H\u001b[?25h"]
[19.439878, "o", "\u001b[?25l\u001b[34;258H2\u001b[23;2H\u001b[?25h"]
[19.658968, "o", "\u001b[?25l\bDNSStubListener=no\u001b[23;19H\u001b[K\u001b[34;258H1\u001b[23;1H\u001b[?25h"]
[19.961993, "o", "\u001b[34;1H\u001b[K\u001b[23;1H\u001b[?25l\u001b[34;245H^[\u001b[23;1H"]
[20.064297, "o", "\u001b[34;245H  \u001b[23;1H\u001b[34;255H23,1\u001b[10CAll\u001b[23;1H\u001b[?25h"]
[20.279426, "o", "\u001b[?25l\u001b[34;245H:\u001b[23;1H\u001b[34;245H\u001b[K\u001b[34;1H:\u001b[?2004h\u001b[?25h"]
[20.446765, "o", "w\u001b[?25l\u001b[?25h"]
[20.524149, "o", "q\u001b[?25l\u001b[?25h"]
[20.638888, "o", "\r\u001b[?25l\u001b[?2004l\"/etc/systemd/resolved.conf\""]
[20.646666, "o", " 23L, 629C written\r\r\r\n\u001b[?2004l\u001b[?1l\u001b>\u001b[?25h\u001b[?1049l\u001b[23;0;0t"]
[20.648933, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[29.758994, "o", "s"]
[29.931653, "o", "u"]
[30.094576, "o", "d"]
[30.168601, "o", "o"]
[30.292781, "o", " "]
[30.398178, "o", "s"]
[30.486931, "o", "y"]
[30.618647, "o", "s"]
[30.691195, "o", "t"]
[30.83152, "o", "e"]
[30.922054, "o", "m"]
[31.022302, "o", "c"]
[31.210776, "o", "t"]
[31.288655, "o", "l"]
[31.436555, "o", " "]
[31.614045, "o", "r"]
[31.705292, "o", "e"]
[31.839963, "o", "s"]
[33.343149, "o", "t"]
[33.47261, "o", "a"]
[33.530077, "o", "r"]
[33.697906, "o", "t"]
[33.88095, "o", " "]
[34.385354, "o", "s"]
[34.568705, "o", "y"]
[34.657774, "o", "s"]
[34.779308, "o", "t"]
[34.881692, "o", "e"]
[34.973838, "o", "m"]
[35.110726, "o", "d"]
[35.248082, "o", "-"]
[35.481531, "o", "r"]
[35.537633, "o", "e"]
[35.732083, "o", "s"]
[35.808539, "o", "o"]
[35.943977, "o", "l"]
[36.019894, "o", "v"]
[36.171319, "o", "e"]
[36.33208, "o", "d"]
[36.481457, "o", "\r\n"]
[36.591318, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[41.19944, "o", "# 查看 docker 镜像"]
[41.398673, "o", "\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[41.85391, "o", "d"]
[41.952242, "o", "o"]
[42.037844, "o", "c"]
[42.133557, "o", "k"]
[42.240005, "o", "e"]
[42.413417, "o", "r"]
[42.801963, "o", "\b\b\b\b\b\b"]
[43.009071, "o", "sdocker\b\b\b\b\b\b"]
[43.126554, "o", "udocker\b\b\b\b\b\b"]
[43.185845, "o", "\u001b[Cdocker\b\b\b\b\b\b"]
[43.311942, "o", "odocker\b\b\b\b\b\b"]
[43.371986, "o", " docker\b\b\b\b\b\b"]
[43.606962, "o", "\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C"]
[44.047398, "o", "i"]
[44.376688, "o", "\b\u001b[K"]
[44.488822, "o", " "]
[44.657498, "o", "i"]
[44.784813, "o", "m"]
[44.957154, "o", "g"]
[45.059572, "o", "a"]
[45.36818, "o", "e"]
[45.686895, "o", "\b\u001b[K"]
[45.842701, "o", "\b\u001b[K"]
[45.986071, "o", "\b\u001b[K"]
[46.079438, "o", "a"]
[46.17132, "o", "g"]
[46.264194, "o", "e"]
[46.340636, "o", " "]
[46.483885, "o", "l"]
[46.589858, "o", "s"]
[46.701043, "o", "\r\n"]
[46.749821, "o", "REPOSITORY     TAG       IMAGE ID       CREATED       SIZE\r\n4km3/dnsmasq"]
[46.755708, "o", "   latest    6edc0f9df6c1   2 weeks ago   6.98MB\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[50.896935, "o", "# 如果没有 dnsmasq 镜像，可以用 docker pull 4km3/dnsmasq 实现"]
[51.536149, "o", "\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[55.62838, "o", "# 创建 docker 容器"]
[55.862321, "o", "\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[63.281183, "o", "sudo docker run -d -p 53:53/tcp -p 53:53/udp --cap-add=NET_ADMIN --name dnsmasq 4km3/dnsmasq"]
[63.877154, "o", "\r\n"]
[63.933101, "o", "docker: Error response from daemon: Conflict. The container name \"/dnsmasq\" is already in use by container \"406ce4d01cc92ed5d752c1298db793af4973b11c069c6c61bafc7b98f8f921b4\". You have to remove (or rename) that container to be able to reuse that name.\r\nSee 'docker run --help'.\r\n"]
[63.935923, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[69.994294, "o", "s"]
[70.073786, "o", "u"]
[70.175156, "o", "d"]
[70.261609, "o", "o"]
[70.357834, "o", " "]
[70.46513, "o", "d"]
[70.572406, "o", "o"]
[70.68373, "o", "c"]
[70.784186, "o", "k"]
[70.895386, "o", "e"]
[71.088077, "o", "r"]
[71.226469, "o", " "]
[71.395934, "o", "c"]
[71.472131, "o", "o"]
[71.609356, "o", "n"]
[71.735196, "o", "t"]
[71.967943, "o", "a"]
[72.087679, "o", "i"]
[72.212695, "o", "n"]
[72.308314, "o", "e"]
[72.506504, "o", "r"]
[72.644237, "o", " "]
[72.911222, "o", "r"]
[72.969147, "o", "m"]
[73.092455, "o", " "]
[74.036013, "o", "d"]
[74.176676, "o", "n"]
[74.287463, "o", "s"]
[74.610231, "o", "m"]
[74.748199, "o", "a"]
[74.915368, "o", "s"]
[75.076261, "o", "q"]
[75.210897, "o", "\r\n"]
[75.287672, "o", "dnsmasq\r\n"]
[75.292505, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[76.486709, "o", "sudo docker run -d -p 53:53/tcp -p 53:53/udp --cap-add=NET_ADMIN --name dnsmasq 4km3/dnsmasq"]
[76.876714, "o", "\r\n"]
[77.079963, "o", "5cf63c690a5136c9cc4c80c0b33dfa4c792fdb3d134499b0ec731713e8038a24\r\n"]
[78.781734, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[87.300479, "o", "d"]
[87.796815, "o", "\b\u001b[K"]
[88.152267, "o", "d"]
[88.513765, "o", "\b\u001b[K"]
[88.713797, "o", "#"]
[88.880722, "o", " "]
[90.936529, "o", "进入"]
[91.988105, "o", "容器"]
[93.043843, "o", "以"]
[94.216648, "o", "编辑"]
[96.259393, "o", "配置"]
[97.531719, "o", "文件"]
[97.781526, "o", "\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[98.839942, "o", "d"]
[98.957618, "o", "o"]
[99.061889, "o", "c"]
[99.162252, "o", "k"]
[99.323301, "o", "e"]
[99.504876, "o", "r"]
[99.613751, "o", " "]
[99.783822, "o", "e"]
[99.964849, "o", "x"]
[100.145889, "o", "e"]
[100.375806, "o", "c"]
[100.554367, "o", " "]
[100.758765, "o", "0"]
[101.143941, "o", "\b\u001b[K"]
[101.28925, "o", "-"]
[101.512855, "o", "i"]
[101.656151, "o", "t"]
[101.850318, "o", " "]
[102.739205, "o", "d"]
[102.86763, "o", "n"]
[102.959022, "o", "s"]
[103.170057, "o", "m"]
[103.296483, "o", "a"]
[103.466635, "o", "s"]
[103.686324, "o", "q"]
[104.206361, "o", " "]
[105.225525, "o", "b"]
[105.570184, "o", "\b\u001b[K"]
[106.145169, "o", "/bi"]
[106.206677, "o", "n"]
[106.468039, "o", "/"]
[106.961441, "o", "s"]
[107.033225, "o", "h"]
[107.20973, "o", "\r\n"]
[107.240725, "o", "Got permission denied while trying to connect to the Docker daemon socket at unix:///var/run/docker.sock: Get \"http://%2Fvar%2Frun%2Fdocker.sock/v1.24/containers/dnsmasq/json\": dial unix /var/run/docker.sock: connect: permission denied\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[108.464888, "o", "\u0007docker exec -it dnsmasq /bin/sh"]
[108.758453, "o", "\r\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C"]
[108.974523, "o", "\u001b[1@s"]
[109.100735, "o", "\u001b[1@u"]
[109.148579, "o", "\u001b[C\u001b[1@d\b"]
[109.311891, "o", "\u001b[1@o"]
[109.420235, "o", "\u001b[1@ "]
[109.542861, "o", "\r\n"]
[109.682699, "o", "/ # \u001b[6n"]
[109.685808, "o", "\r/ # \u001b[J"]
[111.008764, "o", "s"]
[111.367619, "o", "\b\u001b[J"]
[111.520514, "o", "v"]
[111.606659, "o", "i"]
[111.720142, "o", " "]
[111.956256, "o", "/"]
[112.140928, "o", "e"]
[112.26775, "o", "t"]
[112.384699, "o", "c"]
[112.653797, "o", "\b\u001b[J"]
[112.811082, "o", "\b\u001b[J"]
[112.947535, "o", "\b\u001b[J"]
[113.097772, "o", "\b\u001b[J"]
[113.255364, "o", "\b\u001b[J"]
[113.390116, "o", "\b\u001b[J"]
[113.544364, "o", "\b\u001b[J"]
[113.761957, "o", "m"]
[113.90506, "o", "k"]
[114.01682, "o", "d"]
[114.104689, "o", "i"]
[114.235129, "o", "r"]
[114.303665, "o", " "]
[114.556542, "o", "/"]
[115.145822, "o", "e"]
[115.230996, "o", "t"]
[115.333003, "o", "c"]
[115.51483, "o", "/"]
[115.822194, "o", "d"]
[115.97038, "o", "n"]
[116.067948, "o", "s"]
[116.732402, "o", "m"]
[116.848695, "o", "a"]
[117.003458, "o", "s"]
[117.185545, "o", "q"]
[119.503714, "o", "\r\n/ # \u001b[6n"]
[120.689789, "o", "l"]
[120.770038, "o", "s"]
[120.845747, "o", " "]
[121.268617, "o", "/"]
[121.371471, "o", "e"]
[121.525111, "o", "t"]
[121.623907, "o", "c"]
[121.795132, "o", "/"]
[122.712808, "o", "d"]
[122.825802, "o", "n"]
[122.91065, "o", "s"]
[123.093306, "o", "m"]
[123.238491, "o", "a"]
[123.393359, "o", "s"]
[123.626471, "o", "q"]
[123.86972, "o", "\r\n/ # \u001b[6n"]
[126.57786, "o", "v"]
[126.7002, "o", "i"]
[126.829329, "o", " "]
[127.093249, "o", "/"]
[127.196425, "o", "e"]
[127.371999, "o", "t"]
[127.485932, "o", "c"]
[127.680554, "o", "/"]
[128.393374, "o", "d"]
[128.522777, "o", "n"]
[128.642573, "o", "s"]
[128.94455, "o", "m"]
[129.077624, "o", "a"]
[129.231277, "o", "s"]
[129.448316, "o", "q"]
[131.293489, "o", "/"]
[134.727107, "o", "re"]
[136.041264, "o", "s"]
[136.193011, "o", "o"]
[136.399582, "o", "l"]
[136.5303, "o", "l"]
[137.093771, "o", "\b\u001b[J"]
[137.210881, "o", "v"]
[137.418777, "o", "."]
[137.827447, "o", "d"]
[137.908709, "o", "n"]
[138.000332, "o", "s"]
[138.229193, "o", "m"]
[138.698862, "o", "a"]
[138.866489, "o", "s"]
[139.047049, "o", "q"]
[139.530907, "o", "\r\n\u001b[?1049h\u001b[H\u001b[J\u001b[2;1H~\u001b[3;1H~\u001b[4;1H~\u001b[5;1H~\u001b[6;1H~\u001b[7;1H~\u001b[8;1H~\u001b[9;1H~\u001b[10;1H~\u001b[11;1H~\u001b[12;1H~\u001b[13;1H~\u001b[14;1H~\u001b[15;1H~\u001b[16;1H~\u001b[17;1H~\u001b[18;1H~\u001b[19;1H~\u001b[20;1H~\u001b[21;1H~\u001b[22;1H~\u001b[23;1H~\u001b[24;1H~\u001b[25;1H~\u001b[26;1H~\u001b[27;1H~\u001b[28;1H~\u001b[29;1H~\u001b[30;1H~\u001b[31;1H~\u001b[32;1H~\u001b[33;1H~\u001b[1;1H\u001b[34;1H\u001b[K- /etc/dnsmasq/resolv.dnsmasq 1/1 100%\u001b[1;1H"]
[140.739701, "o", "\u001b[1;1H\u001b[34;1H\u001b[K\u001b[7mPattern not found\u001b[m\u001b[1;1H"]
[142.025807, "o", "\u001b[34;1H\u001b[K- /etc/dnsmasq/resolv.dnsmasq 1/1 100%\u001b[1;1H\u001b[1;1H\u001b[34;1H\u001b[KI /etc/dnsmasq/resolv.dnsmasq 1/1 100%\u001b[1;1H"]
[142.604707, "o", "\u001b[1;1Hn\u001b[1;2H"]
[142.680155, "o", "\u001b[1;2Ha\u001b[1;3H"]
[142.788982, "o", "\u001b[1;3Hm\u001b[1;4H"]
[142.929166, "o", "\u001b[1;4He\u001b[1;5H"]
[143.289541, "o", "\u001b[1;5Hs\u001b[1;6H"]
[143.465344, "o", "\u001b[1;6He\u001b[1;7H"]
[143.621313, "o", "\u001b[1;7Hr\u001b[1;8H"]
[143.711396, "o", "\u001b[1;8Hv\u001b[1;9H"]
[143.82767, "o", "\u001b[1;9He\u001b[1;10H"]
[143.975152, "o", "\u001b[1;10Hr\u001b[1;11H"]
[144.10496, "o", "\u001b[1;12H"]
[144.596096, "o", "\u001b[1;12H7\u001b[1;13H"]
[144.789683, "o", "\u001b[1;13H.\u001b[1;14H"]
[144.98604, "o", "\u001b[1;14H7\u001b[1;15H"]
[145.289492, "o", "\u001b[1;14H \u001b[1;14H\u001b[34;1H\u001b[KI /etc/dnsmasq/resolv.dnsmasq [Modified] 1/1 100%\u001b[1;14H"]
[145.43823, "o", "\u001b[1;13H \u001b[1;13H"]
[145.590965, "o", "\u001b[1;12H \u001b[1;12H"]
[146.374505, "o", "\u001b[1;12H8\u001b[1;13H"]
[146.518486, "o", "\u001b[1;13H.\u001b[1;14H"]
[146.708966, "o", "\u001b[1;14H8\u001b[1;15H"]
[146.865454, "o", "\u001b[1;15H.\u001b[1;16H"]
[147.028185, "o", "\u001b[1;16H8\u001b[1;17H"]
[147.21035, "o", "\u001b[1;17H.\u001b[1;18H"]
[147.391944, "o", "\u001b[1;18H8\u001b[1;19H"]
[147.658734, "o", "\u001b[2;1H \u001b[2;1H\u001b[34;1H\u001b[KI /etc/dnsmasq/resolv.dnsmasq [Modified] 2/2 100%\u001b[2;1H"]
[148.024775, "o", "\u001b[2;1Hn\u001b[2;2H"]
[148.272205, "o", "\u001b[2;2Ha\u001b[2;3H"]
[148.439835, "o", "\u001b[2;3Hm\u001b[2;4H"]
[148.70692, "o", "\u001b[2;4He\u001b[2;5H"]
[149.082127, "o", "\u001b[2;5Hs\u001b[2;6H"]
[149.257482, "o", "\u001b[2;6He\u001b[2;7H"]
[149.421749, "o", "\u001b[2;7Hr\u001b[2;8H"]
[149.514973, "o", "\u001b[2;8Hv\u001b[2;9H"]
[149.602287, "o", "\u001b[2;9He\u001b[2;10H"]
[149.76857, "o", "\u001b[2;10Hr\u001b[2;11H"]
[150.424193, "o", "\u001b[2;12H"]
[150.547424, "o", "\u001b[2;12H1\u001b[2;13H"]
[150.706594, "o", "\u001b[2;13H.\u001b[2;14H"]
[150.825618, "o", "\u001b[2;14H1\u001b[2;15H"]
[150.924899, "o", "\u001b[2;15H.\u001b[2;16H"]
[151.057613, "o", "\u001b[2;16H1\u001b[2;17H"]
[151.192221, "o", "\u001b[2;17H.\u001b[2;18H"]
[151.540864, "o", "\u001b[2;18H1\u001b[2;19H"]
[152.064094, "o", "\u001b[2;18H\u001b[34;1H\u001b[K- /etc/dnsmasq/resolv.dnsmasq [Modified] 2/2 100%\u001b[2;18H"]
[152.612405, "o", "\u001b[34;1H\u001b[K:"]
[152.770566, "o", "w"]
[152.881164, "o", "q"]
[153.019553, "o", "\u001b[2;18H\u001b[2;18H\u001b[34;1H\u001b[K'/etc/dnsmasq/resolv.dnsmasq' 2L, 38C\u001b[2;18H\u001b[34;1H\u001b[K\u001b[?1049l/ # \u001b[6n"]
[154.455888, "o", "v"]
[154.548226, "o", "i"]
[154.677596, "o", " "]
[154.909483, "o", "/"]
[155.024701, "o", "e"]
[155.16354, "o", "t"]
[155.259194, "o", "c"]
[155.440031, "o", "/"]
[155.886252, "o", "d"]
[156.00164, "o", "n"]
[156.110297, "o", "s"]
[156.73966, "o", "m"]
[156.862579, "o", "a"]
[156.992448, "o", "s"]
[157.195566, "o", "q"]
[157.730481, "o", "/"]
[159.069535, "o", "d"]
[159.229541, "o", "n"]
[159.325146, "o", "s"]
[159.667787, "o", "m"]
[159.783711, "o", "a"]
[159.980413, "o", "s"]
[160.333035, "o", "q"]
[160.72307, "o", "h"]
[160.837988, "o", "o"]
[160.924672, "o", "s"]
[161.137129, "o", "t"]
[161.357859, "o", "s"]
[161.79919, "o", "\r\n\u001b[?1049h\u001b[H\u001b[J\u001b[2;1H~\u001b[3;1H~\u001b[4;1H~\u001b[5;1H~\u001b[6;1H~\u001b[7;1H~\u001b[8;1H~\u001b[9;1H~\u001b[10;1H~\u001b[11;1H~\u001b[12;1H~\u001b[13;1H~\u001b[14;1H~\u001b[15;1H~\u001b[16;1H~\u001b[17;1H~\u001b[18;1H~\u001b[19;1H~\u001b[20;1H~\u001b[21;1H~\u001b[22;1H~\u001b[23;1H~\u001b[24;1H~\u001b[25;1H~\u001b[26;1H~\u001b[27;1H~\u001b[28;1H~\u001b[29;1H~\u001b[30;1H~\u001b[31;1H~\u001b[32;1H~\u001b[33;1H~\u001b[1;1H\u001b[34;1H\u001b[K- /etc/dnsmasq/dnsmasqhosts 1/1 100%\u001b[1;1H"]
[162.939501, "o", "\u001b[1;1H\u001b[34;1H\u001b[KI /etc/dnsmasq/dnsmasqhosts 1/1 100%\u001b[1;1H"]
[163.563981, "o", "\u001b[1;1H1\u001b[1;2H"]
[163.895366, "o", "\u001b[1;2H0\u001b[1;3H"]
[164.060206, "o", "\u001b[1;3H.\u001b[1;4H"]
[164.303261, "o", "\u001b[1;4H1\u001b[1;5H"]
[164.43887, "o", "\u001b[1;5H0\u001b[1;6H"]
[164.634068, "o", "\u001b[1;6H.\u001b[1;7H"]
[165.065542, "o", "\u001b[1;7H0\u001b[1;8H"]
[165.2107, "o", "\u001b[1;8H.\u001b[1;9H"]
[165.413585, "o", "\u001b[1;9H1\u001b[1;10H"]
[166.057572, "o", "\u001b[1;11H"]
[166.311823, "o", "\u001b[1;11Ha\u001b[1;12H"]
[166.433972, "o", "\u001b[1;12Hy\u001b[1;13H"]
[166.520805, "o", "\u001b[1;13Ha\u001b[1;14H"]
[166.618781, "o", "\u001b[1;14Hk\u001b[1;15H"]
[166.727585, "o", "\u001b[1;15Ha\u001b[1;16H"]
[166.932754, "o", "\u001b[1;16H.\u001b[1;17H"]
[167.190638, "o", "\u001b[1;17Hl\u001b[1;18H"]
[167.376412, "o", "\u001b[1;18Ho\u001b[1;19H"]
[167.457744, "o", "\u001b[1;19Hc\u001b[1;20H"]
[167.544278, "o", "\u001b[1;20Ha\u001b[1;21H"]
[167.656981, "o", "\u001b[1;21Hl\u001b[1;22H"]
[167.975392, "o", "\u001b[1;21H\u001b[34;1H\u001b[K- /etc/dnsmasq/dnsmasqhosts [Modified] 1/1 100%\u001b[1;21H"]
[168.367664, "o", "\u001b[34;1H\u001b[K:"]
[168.565988, "o", "w"]
[168.691555, "o", "q"]
[168.998348, "o", "\u001b[1;21H\u001b[1;21H\u001b[34;1H\u001b[K'/etc/dnsmasq/dnsmasqhosts' 1L, 22C\u001b[1;21H\u001b[34;1H\u001b[K\u001b[?1049l/ # \u001b[6n"]
[171.257772, "o", "v"]
[171.383339, "o", "i"]
[171.536112, "o", " "]
[172.567814, "o", "/"]
[172.966996, "o", "e"]
[173.040658, "o", "t"]
[173.190884, "o", "c"]
[173.364075, "o", "/"]
[173.99704, "o", "d"]
[174.118347, "o", "n"]
[174.232072, "o", "s"]
[174.71903, "o", "\u0007\r/ # vi /etc/dnsmasq\u001b[J"]
[175.81827, "o", "."]
[175.863458, "o", "c"]
[176.062264, "o", "\r/ # vi /etc/dnsmasq.conf \u001b[J"]
[176.508413, "o", "\r\n\u001b[?1049h\u001b[H\u001b[J\u001b[1;1H# Configuration file for dnsmasq.\u001b[2;1H#\u001b[3;1H# Format is one option per line, legal options are the same\u001b[4;1H# as the long options legal on the command line. See\u001b[5;1H# \"/usr/sbin/dnsmasq --help\" or \"man 8 dnsmasq\" for details.\u001b[7;1H# Listen on this specific port instead of the standard DNS port\u001b[8;1H# (53). Setting this to zero completely disables DNS function,\u001b[9;1H# leaving only DHCP and/or TFTP.\u001b[10;1H#port=5353\u001b[12;1H# The following two options make you a better netizen, since they\u001b[13;1H# tell dnsmasq to filter out queries which the public DNS cannot\u001b[14;1H# answer, and which load the servers (especially the root servers)\u001b[15;1H# unnecessarily. If you have a dial-on-demand link they also stop\u001b[16;1H# these requests from bringing up the link unnecessarily.\u001b[18;1H# Never forward plain names (without a dot or domain part)\u001b[19;1H#domain-needed\u001b[20;1H# Never forward addresses in the non-routed address spaces.\u001b[21;1H#bogus-priv\u001b[23;1H# Uncomment these to enable DNSSEC validation and ca"]
[176.508494, "o", "ching:\u001b[24;1H# (Requires dnsmasq to be built with DNSSEC option.)\u001b[25;1H#conf-file=%%PREFIX%%/share/dnsmasq/trust-anchors.conf\u001b[26;1H#dnssec\u001b[28;1H# Replies which are not DNSSEC signed may be legitimate, because the domain\u001b[29;1H# is unsigned, or may be forgeries. Setting this option tells dnsmasq to\u001b[30;1H# check that an unsigned reply is OK, by finding a secure proof that a DS\u001b[31;1H# record somewhere between the root and the domain does not exist.\u001b[32;1H# The cost of setting this is that even queries in unsigned domains will need\u001b[33;1H# one or more extra DNS queries to verify.\u001b[1;1H\u001b[34;1H\u001b[K- /etc/dnsmasq.conf 1/679 0%\u001b[1;1H"]
[177.486192, "o", "\u001b[34;1H\u001b[K/"]
[178.775782, "o", "r"]
[178.839114, "o", "e"]
[179.076889, "o", "s"]
[179.155788, "o", "o"]
[179.336803, "o", "l"]
[179.938718, "o", "v"]
[180.18674, "o", "\u001b[1;1H\u001b[1;3Htell dnsmasq to filter out queries which the public DNS cannot\u001b[2;3Hanswer, and which load the servers (especially the root servers)\u001b[3;3Hunnecessarily. If you have a dial-on-demand link they also stop\u001b[4;3Hthese requests from bringing up the link unnecessarily.\u001b[5;1H                                                            \u001b[6;1H# Never forward plain names (without a dot or domain part)\u001b[7;2Hdomain-needed                                                 \u001b[8;3HNever forward addresses in the non-routed address spaces.   \u001b[9;2Hbogus-priv                     \u001b[10;1H          \u001b[11;1H# Uncomment these to enable DNSSEC validation and caching:\u001b[12;3H(Requires dnsmasq to be built with DNSSEC option.)             \u001b[13;2Hconf-file=%%PREFIX%%/share/dnsmasq/trust-anchors.conf          \u001b[14;2Hdnssec                                                           \u001b[15;1H                                                                 \u001b[16;3HReplies which are not DNSSEC signed may be legitimate, because the domain\u001b[1"]
[180.186892, "o", "7;1H# is unsigned, or may be forgeries. Setting this option tells dnsmasq to\u001b[18;3Hcheck that an unsigned reply is OK, by finding a secure proof that a DS\u001b[19;2H record somewhere between the root and the domain does not exist.\u001b[20;3HThe cost of setting this is that even queries in unsigned domains will need\u001b[21;2H one or more extra DNS queries to verify.\u001b[22;1H#dnssec-check-unsigned\u001b[23;1H                                                          \u001b[24;3HUncomment this to filter useless windows-originated DNS requests\u001b[25;2H which can trigger dial-on-demand links needlessly.  \u001b[26;2H Note that (amongst other things) this blocks all SRV requests,\u001b[27;1H# so don't use it if you use eg Kerberos, SIP, XMMP or Google-talk.\u001b[28;3HThis option only affects forwarding, SRV records originating for         \u001b[29;3Hdnsmasq (via srv-host= lines) are not suppressed by it.               \u001b[30;2Hfilterwin2k                                                             \u001b[31;1H                                                        "]
[180.187177, "o", "          \u001b[32;3HChange this line if you want dns to get its upstream servers from          \u001b[33;3Hsomewhere other that /etc/resolv.conf   \u001b[33;29H\u001b[34;1H\u001b[K- /etc/dnsmasq.conf 45/679 6%\u001b[33;29H"]
[182.705709, "o", "\u001b[34;1H\u001b[K/"]
[182.845465, "o", "\u001b[33;29H\u001b[1;3Hanswer, and which load the servers (especially the root servers)\u001b[2;3Hunnecessarily. If you have a dial-on-demand link they also stop \u001b[3;3Hthese requests from bringing up the link unnecessarily.        \u001b[4;1H                                                         \u001b[5;1H# Never forward plain names (without a dot or domain part)\u001b[6;2Hdomain-needed                                            \u001b[7;2H Never forward addresses in the non-routed address spaces.\u001b[8;2Hbogus-priv                                                \u001b[9;1H           \u001b[10;1H# Uncomment these to enable DNSSEC validation and caching:\u001b[11;3H(Requires dnsmasq to be built with DNSSEC option.)      \u001b[12;2Hconf-file=%%PREFIX%%/share/dnsmasq/trust-anchors.conf\u001b[13;2Hdnssec                                               \u001b[14;1H       \u001b[15;1H# Replies which are not DNSSEC signed may be legitimate, because the domain\u001b[16;3His unsigned, or may be forgeries. Setting this option tells dnsmasq to   \u001b[17;3Hcheck that an unsigned reply is OK, by fi"]
[182.845653, "o", "nding a secure proof that a DS\u001b[18;3Hrecord somewhere between the root and the domain does not exist.       \u001b[19;3HThe cost of setting this is that even queries in unsigned domains will need\u001b[20;3Hone or more extra DNS queries to verify.                                   \u001b[21;2Hdnssec-check-unsigned                    \u001b[22;1H                      \u001b[23;1H# Uncomment this to filter useless windows-originated DNS requests\u001b[24;3Hwhich can trigger dial-on-demand links needlessly.              \u001b[25;3HNote that (amongst other things) this blocks all SRV requests,\u001b[26;3Hso don't use it if you use eg Kerberos, SIP, XMMP or Google-talk.\u001b[27;3HThis option only affects forwarding, SRV records originating for \u001b[28;3Hdnsmasq (via srv-host= lines) are not suppressed by it.         \u001b[29;2Hfilterwin2k                                             \u001b[30;1H            \u001b[31;1H# Change this line if you want dns to get its upstream servers from\u001b[32;3Hsomewhere other that /etc/resolv.conf                            \u001b[33;2Hresolv-file="]
[182.845833, "o", "                          \u001b[33;2H\u001b[34;1H\u001b[K- /etc/dnsmasq.conf 46/679 6%\u001b[33;2H"]
[184.434241, "o", "\u001b[33;2H\u001b[34;1H\u001b[KI /etc/dnsmasq.conf 46/679 6%\u001b[33;2H"]
[185.376215, "o", "\u001b[33;1Hresolv-file= \u001b[33;1H"]
[185.87137, "o", "\u001b[33;2H\u001b[34;1H\u001b[KI /etc/dnsmasq.conf [Modified] 46/679 6%\u001b[33;2H"]
[186.034941, "o", "\u001b[33;3H"]
[186.546817, "o", "\u001b[33;4H"]
[186.625493, "o", "\u001b[33;5H"]
[186.706321, "o", "\u001b[33;6H"]
[186.82296, "o", "\u001b[33;7H"]
[186.867146, "o", "\u001b[33;8H"]
[186.945425, "o", "\u001b[33;9H"]
[187.068259, "o", "\u001b[33;10H"]
[187.119469, "o", "\u001b[33;11H"]
[187.201861, "o", "\u001b[33;12H"]
[187.291052, "o", "\u001b[33;13H"]
[187.717567, "o", "\u001b[33;13H/\u001b[33;14H"]
[187.953733, "o", "\u001b[33;14He\u001b[33;15H"]
[188.067067, "o", "\u001b[33;15Ht\u001b[33;16H"]
[188.16107, "o", "\u001b[33;16Hc\u001b[33;17H"]
[188.375941, "o", "\u001b[33;17H/\u001b[33;18H"]
[189.293635, "o", "\u001b[33;18Hd\u001b[33;19H"]
[189.414718, "o", "\u001b[33;19Hn\u001b[33;20H"]
[189.543639, "o", "\u001b[33;20Hs\u001b[33;21H"]
[189.999124, "o", "\u001b[33;21Hm\u001b[33;22H"]
[190.115187, "o", "\u001b[33;22Ha\u001b[33;23H"]
[190.281854, "o", "\u001b[33;23Hs\u001b[33;24H"]
[190.496902, "o", "\u001b[33;24Hq\u001b[33;25H"]
[190.759769, "o", "\u001b[33;25H.\u001b[33;26H"]
[191.274549, "o", "\u001b[33;25H \u001b[33;25H"]
[191.383831, "o", "\u001b[33;25H/\u001b[33;26H"]
[191.917147, "o", "\u001b[33;26Hr\u001b[33;27H"]
[191.99711, "o", "\u001b[33;27He\u001b[33;28H"]
[192.192439, "o", "\u001b[33;28Hs\u001b[33;29H"]
[192.397407, "o", "\u001b[33;29Ho\u001b[33;30H"]
[192.5706, "o", "\u001b[33;30Hl\u001b[33;31H"]
[192.679718, "o", "\u001b[33;31Hv\u001b[33;32H"]
[192.833425, "o", "\u001b[33;32He\u001b[33;33H"]
[193.440988, "o", "\u001b[33;32H \u001b[33;32H"]
[193.56028, "o", "\u001b[33;32H.\u001b[33;33H"]
[194.118263, "o", "\u001b[33;33Hr\u001b[33;34H"]
[194.205164, "o", "\u001b[33;34He\u001b[33;35H"]
[194.509047, "o", "\u001b[33;34H \u001b[33;34H"]
[194.64945, "o", "\u001b[33;33H \u001b[33;33H"]
[194.749113, "o", "\u001b[33;33Hd\u001b[33;34H"]
[194.857105, "o", "\u001b[33;34Hn\u001b[33;35H"]
[194.941318, "o", "\u001b[33;35Hs\u001b[33;36H"]
[195.292377, "o", "\u001b[33;36Hm\u001b[33;37H"]
[195.453277, "o", "\u001b[33;37Ha\u001b[33;38H"]
[195.597144, "o", "\u001b[33;38Hs\u001b[33;39H"]
[195.805772, "o", "\u001b[33;39Hq\u001b[33;40H"]
[196.257643, "o", "\u001b[33;39H\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 46/679 6%\u001b[33;39H"]
[197.488455, "o", "\u001b[34;1H\u001b[K/"]
[199.003358, "o", "d"]
[199.528593, "o", "\b \b"]
[199.614321, "o", "a"]
[199.757056, "o", "d"]
[199.921835, "o", "d"]
[200.309827, "o", "n"]
[200.541432, "o", "\u001b[33;39H\u001b[1;2Hno-dhcp-interface=                                               \u001b[2;1H                                                                 \u001b[3;3HOn systems which support it, dnsmasq binds the wildcard address,\u001b[4;1H# even when it is listening on only some interfaces. It then discards\u001b[5;3Hrequests that it shouldn't reply to. This has the advantage of\u001b[6;2H working even when interfaces come and go and change address. If you\u001b[7;3Hwant dnsmasq to really bind only the interfaces it is listening on,\u001b[8;2H uncomment this option. About the only time you may need this is when\u001b[9;1H# running another nameserver on the same machine.\u001b[10;2Hbind-interfaces                                          \u001b[11;1H                                                    \u001b[12;2H If you don't want dnsmasq to read /etc/hosts, uncomment the\u001b[13;2H following line.\u001b[14;1H#no-hosts\u001b[15;3Hor if you want it to read another file, as well as /etc/hosts, use       \u001b[16;3Hthis.                                                               "]
[200.541544, "o", "  \u001b[17;2Haddn-hosts=/etc/banner_add_hosts                                        \u001b[18;1H                                                                  \u001b[19;3HSet this (and domain: see below) if you want to have a domain              \u001b[20;3Hautomatically added to simple names in a hosts-file.\u001b[21;2Hexpand-hosts         \u001b[23;3HSet the domain for dnsmasq. this is optional, but if it is set, it\u001b[24;3Hdoes the following things.                        \u001b[25;3H1) Allows DHCP hosts to have fully qualified domain names, as long\u001b[26;3H    as the domain part matches this setting.                     \u001b[27;3H2) Sets the \"domain\" DHCP option thereby potentially setting the\u001b[28;3H   domain of all systems configured by DHCP            \u001b[29;2H 3) Provides the domain part for \"expand-hosts\"\u001b[30;1H#domain=thekelleys.org.uk\u001b[31;1H                                                                   \u001b[32;3HSet a different domain for a particular subnet\u001b[33;1H#domain=wireless.thekelleys.org.uk,***********/24\u001b[17;2H\u001b[34;1H\u001b[K- /etc/"]
[200.541573, "o", "dnsmasq.conf [Modified] 131/679 19%\u001b[17;2H"]
[202.838621, "o", "\u001b[34;1H\u001b[K/"]
[203.004959, "o", "\u001b[17;2H\u001b[17;2H\u001b[34;1H\u001b[K\u001b[7msearch hit BOTTOM, continuing at TOP\u001b[m\u001b[17;2H"]
[204.012313, "o", "\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 131/679 19%\u001b[17;2H\u001b[34;1H\u001b[K/"]
[204.258888, "o", "\u001b[17;2H\u001b[17;2H\u001b[34;1H\u001b[K\u001b[7msearch hit BOTTOM, continuing at TOP\u001b[m\u001b[17;2H"]
[204.786821, "o", "\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 131/679 19%\u001b[17;2H\u001b[18;1H\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 132/679 19%\u001b[18;1H"]
[204.883358, "o", "\u001b[34;1H\u001b[K/"]
[205.770313, "o", "a"]
[205.924702, "o", "d"]
[206.081781, "o", "d"]
[206.486418, "o", "n"]
[207.27236, "o", "\u001b[18;1H\u001b[17;2H\u001b[34;1H\u001b[K\u001b[7msearch hit BOTTOM, continuing at TOP\u001b[m\u001b[17;2H"]
[209.195298, "o", "\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 131/679 19%\u001b[17;2H\u001b[34;1H\u001b[K/"]
[209.460945, "o", "\u001b[17;2H\u001b[17;2H\u001b[34;1H\u001b[K\u001b[7msearch hit BOTTOM, continuing at TOP\u001b[m\u001b[17;2H"]
[209.836523, "o", "\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 131/679 19%\u001b[17;2H\u001b[34;1H\u001b[K/"]
[210.005608, "o", "\u001b[17;2H\u001b[17;2H\u001b[34;1H\u001b[K\u001b[7msearch hit BOTTOM, continuing at TOP\u001b[m\u001b[17;2H"]
[212.907214, "o", "\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 131/679 19%\u001b[17;2H\u001b[17;2H\u001b[34;1H\u001b[KI /etc/dnsmasq.conf [Modified] 131/679 19%\u001b[17;2H"]
[214.390245, "o", "\u001b[17;1H\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 131/679 19%\u001b[17;1H"]
[218.446213, "o", "\u001b[17;34H\u001b[34;1H\u001b[KI /etc/dnsmasq.conf [Modified] 131/679 19%\u001b[17;34H"]
[219.147539, "o", "\u001b[19;1H                                                               \u001b[20;3HSet this (and domain: see below) if you want to have a domain\u001b[21;2H automatically added to simple names in a hosts-file.\u001b[22;1H#expand-hosts\u001b[23;1H                                                                    \u001b[24;3HSet the domain for dnsmasq. this is optional, but if it is set, it\u001b[25;3Hdoes the following things.                                        \u001b[26;3H1) Allows DHCP hosts to have fully qualified domain names, as long\u001b[27;3H    as the domain part matches this setting.                    \u001b[28;3H2) Sets the \"domain\" DHCP option thereby potentially setting the\u001b[29;3H   domain of all systems configured by DHCP   \u001b[30;2H 3) Provides the domain part for \"expand-hosts\"\u001b[31;1H#domain=thekelleys.org.uk\u001b[32;1H                                                \u001b[33;2H Set a different domain for a particular subnet \u001b[18;1H\u001b[34;1H\u001b[KI /etc/dnsmasq.conf [Modified] 132/679 19%\u001b[18;1H"]
[219.984276, "o", "\u001b[18;1Ha\u001b[18;2H"]
[220.148463, "o", "\u001b[18;2Hd\u001b[18;3H"]
[220.312527, "o", "\u001b[18;3Hd\u001b[18;4H"]
[220.698717, "o", "\u001b[18;4Hn\u001b[18;5H"]
[220.959977, "o", "\u001b[18;5H-\u001b[18;6H"]
[221.509204, "o", "\u001b[18;6Hh\u001b[18;7H"]
[221.665149, "o", "\u001b[18;7Ho\u001b[18;8H"]
[221.756187, "o", "\u001b[18;8Hs\u001b[18;9H"]
[221.962842, "o", "\u001b[18;9Ht\u001b[18;10H"]
[222.161977, "o", "\u001b[18;10Hs\u001b[18;11H"]
[222.41396, "o", "\u001b[18;11H/\u001b[18;12H"]
[222.726219, "o", "\u001b[18;11H \u001b[18;11H\u001b[34;1H\u001b[KI /etc/dnsmasq.conf [Modified] 132/680 19%\u001b[18;11H"]
[222.950642, "o", "\u001b[18;11H=\u001b[18;12H"]
[223.487499, "o", "\u001b[18;12H/\u001b[18;13H"]
[223.655929, "o", "\u001b[18;13He\u001b[18;14H"]
[223.796378, "o", "\u001b[18;14Ht\u001b[18;15H"]
[223.873711, "o", "\u001b[18;15Hc\u001b[18;16H"]
[224.069722, "o", "\u001b[18;16H/\u001b[18;17H"]
[224.490785, "o", "\u001b[18;17Hb\u001b[18;18H"]
[224.572901, "o", "\u001b[18;18Ha\u001b[18;19H"]
[224.677554, "o", "\u001b[18;19Hn\u001b[18;20H"]
[224.819406, "o", "\u001b[18;20Hn\u001b[18;21H"]
[224.940253, "o", "\u001b[18;21He\u001b[18;22H"]
[225.111359, "o", "\u001b[18;22Hr\u001b[18;23H"]
[225.483734, "o", "\u001b[18;22H \u001b[18;22H"]
[225.570748, "o", "\u001b[18;21H \u001b[18;21H"]
[225.715096, "o", "\u001b[18;20H \u001b[18;20H"]
[225.861947, "o", "\u001b[18;19H \u001b[18;19H"]
[226.012637, "o", "\u001b[18;18H \u001b[18;18H"]
[226.161241, "o", "\u001b[18;17H \u001b[18;17H"]
[226.362471, "o", "\u001b[18;17Hd\u001b[18;18H"]
[226.475578, "o", "\u001b[18;18Hn\u001b[18;19H"]
[226.573791, "o", "\u001b[18;19Hs\u001b[18;20H"]
[226.795888, "o", "\u001b[18;20H.\u001b[18;21H"]
[227.245344, "o", "\u001b[18;20H \u001b[18;20H"]
[227.441065, "o", "\u001b[18;20Hm\u001b[18;21H"]
[227.53102, "o", "\u001b[18;21Ha\u001b[18;22H"]
[227.709159, "o", "\u001b[18;22Hs\u001b[18;23H"]
[227.913087, "o", "\u001b[18;23Hq\u001b[18;24H"]
[228.15762, "o", "\u001b[18;24H/\u001b[18;25H"]
[228.827746, "o", "\u001b[18;25Hd\u001b[18;26H"]
[228.948502, "o", "\u001b[18;26Hn\u001b[18;27H"]
[229.067235, "o", "\u001b[18;27Hs\u001b[18;28H"]
[229.825436, "o", "\u001b[18;28Hm\u001b[18;29H"]
[229.923598, "o", "\u001b[18;29Ha\u001b[18;30H"]
[230.140801, "o", "\u001b[18;30Hs\u001b[18;31H"]
[230.290149, "o", "\u001b[18;31Hq\u001b[18;32H"]
[231.310898, "o", "\u001b[18;32Hh\u001b[18;33H"]
[231.448679, "o", "\u001b[18;33Ho\u001b[18;34H"]
[231.51032, "o", "\u001b[18;34Hs\u001b[18;35H"]
[231.70144, "o", "\u001b[18;35Ht\u001b[18;36H"]
[231.893028, "o", "\u001b[18;36Hs\u001b[18;37H"]
[233.296222, "o", "\u001b[18;36H\u001b[34;1H\u001b[K- /etc/dnsmasq.conf [Modified] 132/680 19%\u001b[18;36H"]
[233.854336, "o", "\u001b[34;1H\u001b[K:"]
[234.026569, "o", "w"]
[234.105281, "o", "q"]
[234.266363, "o", "\u001b[18;36H\u001b[18;36H\u001b[34;1H\u001b[K'/etc/dnsmasq.conf' 680L, 27443C\u001b[18;36H\u001b[34;1H\u001b[K\u001b[?1049l/ # \u001b[6n"]
[235.891724, "o", "e"]
[236.115382, "o", "x"]
[236.250743, "o", "i"]
[236.389464, "o", "t"]
[237.079725, "o", "\r\n"]
[237.088182, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[238.103552, "o", "d"]
[238.270725, "o", "o"]
[238.674971, "o", "\b\u001b[K"]
[238.815725, "o", "\b\u001b[K"]
[238.917881, "o", "s"]
[239.024321, "o", "u"]
[239.130523, "o", "d"]
[239.230679, "o", "o"]
[239.436475, "o", " "]
[239.589039, "o", "d"]
[239.723199, "o", "o"]
[239.807207, "o", "c"]
[239.905148, "o", "k"]
[240.002427, "o", "e"]
[240.167628, "o", "r"]
[240.264239, "o", " "]
[240.467635, "o", "r"]
[240.518192, "o", "e"]
[240.754388, "o", "s"]
[240.912574, "o", "t"]
[240.994771, "o", "a"]
[241.078355, "o", "r"]
[241.241127, "o", "r"]
[241.245276, "o", "t"]
[241.418677, "o", " "]
[241.663209, "o", "d"]
[241.880081, "o", "\b\u001b[K"]
[242.043987, "o", "\b\u001b[K"]
[242.191281, "o", "\b\u001b[K"]
[242.336451, "o", "\b\u001b[K"]
[242.498463, "o", "t"]
[242.629004, "o", " "]
[242.970974, "o", "d"]
[243.096447, "o", "n"]
[243.210374, "o", "s"]
[243.990725, "o", "m"]
[244.131763, "o", "a"]
[244.309634, "o", "s"]
[244.543347, "o", "q"]
[245.3535, "o", "\r\n"]
[248.306596, "o", "dnsmasq\r\n"]
[248.31038, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[250.089033, "o", "n"]
[250.215008, "o", "s"]
[250.449667, "o", "l"]
[250.633654, "o", "o"]
[250.775795, "o", "o"]
[250.916095, "o", "k"]
[251.117559, "o", "u"]
[251.302533, "o", "p"]
[251.48134, "o", " "]
[252.520392, "o", "a"]
[252.628073, "o", "y"]
[252.724344, "o", "a"]
[252.82804, "o", "k"]
[252.929217, "o", "a"]
[253.092958, "o", "."]
[253.310017, "o", "l"]
[253.474056, "o", "o"]
[253.569898, "o", "c"]
[253.653127, "o", "a"]
[253.752477, "o", "l"]
[254.208672, "o", " "]
[254.480551, "o", "1"]
[255.069335, "o", "\b\u001b[K"]
[255.24528, "o", "\b\u001b[K"]
[255.767571, "o", "\b\u001b[K"]
[255.840374, "o", "\b\u001b[K"]
[255.919582, "o", "\b\u001b[K"]
[256.007349, "o", "\b\u001b[K"]
[256.087014, "o", "\b\u001b[K"]
[256.176379, "o", "\b\u001b[K"]
[256.256067, "o", "\b\u001b[K"]
[256.341782, "o", "\b\u001b[K"]
[256.422273, "o", "\b\u001b[K"]
[256.504297, "o", "\b\u001b[K"]
[256.583927, "o", "\b\u001b[K"]
[256.662761, "o", "\b\u001b[K"]
[256.747941, "o", "\b\u001b[K"]
[256.833569, "o", "\b\u001b[K"]
[256.919822, "o", "\b\u001b[K"]
[257.002557, "o", "\b\u001b[K"]
[257.087542, "o", "\b\u001b[K"]
[257.173371, "o", "\b\u001b[K"]
[257.258176, "o", "\b\u001b[K"]
[257.341555, "o", "\b\u001b[K"]
[257.43198, "o", "\u0007"]
[257.512324, "o", "\u0007"]
[257.687528, "o", "#"]
[257.875608, "o", " "]
[258.83378, "o", "获取"]
[259.147592, "o", " "]
[260.076844, "o", "docker"]
[260.35467, "o", " "]
[261.277123, "o", "容器"]
[261.44008, "o", " "]
[261.929181, "o", "ip"]
[262.121786, "o", "\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[263.700914, "o", "d"]
[263.833585, "o", "o"]
[263.946568, "o", "c"]
[264.046758, "o", "k"]
[264.136856, "o", "e"]
[264.333604, "o", "r"]
[264.466859, "o", " "]
[264.572918, "o", "e"]
[264.777296, "o", "x"]
[264.962905, "o", "e"]
[265.112416, "o", "c"]
[265.231432, "o", " "]
[265.682199, "o", "-"]
[265.909006, "o", "i"]
[266.039245, "o", "t"]
[266.122498, "o", " "]
[267.23048, "o", "d"]
[267.391126, "o", "n"]
[267.619115, "o", "s"]
[268.138558, "o", "m"]
[268.267623, "o", "a"]
[268.475383, "o", "s"]
[268.703136, "o", "q"]
[269.107986, "o", " "]
[269.471313, "o", "i"]
[270.864546, "o", "\b\u001b[K"]
[271.564882, "o", "b"]
[271.709282, "o", "\b\u001b[K"]
[271.926252, "o", "/"]
[272.178883, "o", "b"]
[272.316936, "o", "i"]
[272.444906, "o", "n"]
[272.682077, "o", "/"]
[273.491361, "o", "s"]
[273.577138, "o", "h"]
[273.780888, "o", "\r\n"]
[273.865109, "o", "Got permission denied while trying to connect to the Docker daemon socket at unix:///var/run/docker.sock: Get \"http://%2Fvar%2Frun%2Fdocker.sock/v1.24/containers/dnsmasq/json\": dial unix /var/run/docker.sock: connect: permission denied\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[275.096327, "o", "docker exec -it dnsmasq /bin/sh"]
[275.334704, "o", "\r\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C\u001b[C"]
[275.582288, "o", "\u001b[1@s"]
[275.707147, "o", "\u001b[1@u"]
[275.743152, "o", "\u001b[C\u001b[1@d\b"]
[275.868425, "o", "\u001b[1@o"]
[275.98668, "o", "\u001b[1@ "]
[276.112504, "o", "\r\n"]
[276.256289, "o", "/ # \u001b[6n\r/ # \u001b[J"]
[277.461391, "o", "i"]
[277.637857, "o", "f"]
[277.961925, "o", "c"]
[278.022188, "o", "o"]
[278.129885, "o", "n"]
[278.259632, "o", "f"]
[278.348606, "o", "i"]
[278.461813, "o", "g"]
[278.579356, "o", "\r\neth0      Link encap:Ethernet  HWaddr 02:42:AC:11:00:02  \r\n          inet addr:**********  Bcast:172.17.255.255  Mask:255.255.0.0\r\n          UP BROADCAST RUNNING MULTICAST  MTU:1500  Metric:1\r\n          RX packets:24 errors:0 dropped:0 overruns:0 frame:0\r\n          TX packets:0 errors:0 dropped:0 overruns:0 carrier:0\r\n          collisions:0 txqueuelen:0 \r\n          RX bytes:2008 (1.9 KiB)  TX bytes:0 (0.0 B)\r\n\r\nlo        Link encap:Local Loopback  \r\n          inet addr:127.0.0.1  Mask:255.0.0.0\r\n          UP LOOPBACK RUNNING  MTU:65536  Metric:1\r\n          RX packets:0 errors:0 dropped:0 overruns:0 frame:0\r\n          TX packets:0 errors:0 dropped:0 overruns:0 carrier:0\r\n          collisions:0 txqueuelen:1000 \r\n          RX bytes:0 (0.0 B)  TX bytes:0 (0.0 B)\r\n\r\n/ # \u001b[6n"]
[282.809203, "o", "q"]
[283.415425, "o", "\b\u001b[J"]
[283.528257, "o", "e"]
[283.695733, "o", "x"]
[283.789613, "o", "i"]
[283.901406, "o", "t"]
[284.01561, "o", "\r\n"]
[284.02358, "o", "\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[286.29799, "o", "#"]
[286.502913, "o", " "]
[288.973012, "o", "测试"]
[289.113716, "o", " "]
[289.757605, "o", "DNS"]
[289.950456, "o", "\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[291.371872, "o", "ns"]
[291.562366, "o", "l"]
[291.742618, "o", "o"]
[291.886582, "o", "o"]
[292.016057, "o", "k"]
[292.217294, "o", "u"]
[292.352551, "o", "p"]
[292.491701, "o", " "]
[293.359791, "o", "a"]
[293.463656, "o", "y"]
[293.544532, "o", "a"]
[293.635429, "o", "k"]
[293.738095, "o", "a"]
[293.896842, "o", "."]
[294.121955, "o", "l"]
[294.313434, "o", "o"]
[294.406695, "o", "c"]
[294.505231, "o", "a"]
[294.582893, "o", "l"]
[294.768207, "o", " "]
[295.789139, "o", "**********"]
[296.046543, "o", "\r\n"]
[296.072861, "o", "Server:\t\t**********\r\nAddress:\t**********#53\r\n\r\n"]
[296.110675, "o", "Name:\tayaka.local\r\nAddress: *********\r\n\r\n\u001b]0;neko@dash01:~\u0007[neko@dash01 ~]$ "]
[299.629265, "o", "注销\r\n"]
[299.629498, "o", "Connection to ************* closed.\r\r\n"]
[299.659746, "o", "\u001b]0;~\u0007"]
[299.660503, "o", "\r\n"]
[299.660597, "o", "\u001b]2;yuna@Ayaka-MBP:~\u0007\u001b]1;~\u0007"]
[299.660822, "o", "\u001b[0m\u001b[27m\u001b[24m\u001b[J\u001b[34m~\u001b[39m \u001b[33m4m 55s\u001b[39m\r\n\r\u001b[35m❯\u001b[39m \u001b[K"]
[299.66089, "o", "\u001b[?1h\u001b="]
[299.660962, "o", "\u001b[?2004h"]
[301.151969, "o", "\u001b[?2004l\r\r\n"]
