---
tags:
  - 开发/前端
  - 艺术/设计/Design-Token
  - 规范/设计规范
---

# 什么是设计规范

​		适用于 Web 产品线的人机交互界面设计方面的指导手册。

​		贯穿以用户为中心的设计指导方向，根据 Web产品的特点指定出的一套规范，以达到提升用户体验，控制产品设计质量，提高设计效率的目的。

## 谁去读设计规范

​		该设计规范手册适合界面设计师，用户体验设计师，前台技术工程师，发布支持人员，运营编辑人员的参照。

### 1. 统一识别

​		规范能使页面相同属性单页识别统一，防止混乱，甚至出现严重错误，避免用户在浏览时理解困难

### 2. 节约资源

​		除活动推广等个性页面外，设计其他页面使用本规范标准能极大的减少设计时间，达到节约资源的目的

### 3. 重复利用

​		相同属性单元、页面新建时可执行此标准重复使用，减少无关信息，就是减少对主体信息传达的干扰，利于阅读与信息传递

### 4. 上手简单

​		在招收、加入新设计师或前端时，查看标准能使工作上时间更快，减少出错

## 常见 UI 设计规范

### Material Design

来自 Google（谷歌）

[文档 中文翻译版](https://www.mdui.org/design/material-design/introduction.html)

### Evement UI

来自 饿了么，*Matataki.io 用的是这个*

[文档](https://element-plus.gitee.io/#/zh-CN/component/layout)

### Ant Design

来自 蚂蚁集团

[主页](https://ant.design/index-cn)

## 参考阅读

[给你正确的网站视觉设计规范，不要再犯低级错误了哦！](https://www.bilibili.com/read/cv98502)

[设计规范-Web版](https://www.zcool.com.cn/article/ZMTEyMjY4NA==.html)

[更多主流 UI 设计](https://www.uisdc.com/design-specification-website)
