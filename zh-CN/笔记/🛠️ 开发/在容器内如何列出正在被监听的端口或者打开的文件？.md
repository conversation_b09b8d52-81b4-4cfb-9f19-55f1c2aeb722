---
status: 尚未完成
tags:
  - 计算机/操作系统/Linux/内核/procfs
  - 计算机/操作系统/Linux/内核
  - 计算机/操作系统/Linux/内核/kernel
  - 命令行/netstat
  - 计算机/网络/协议/UDP
  - 计算机/网络/协议/TCP
  - 开发/容器化
  - 开发/云原生/容器运行时接口/CRI
  - 命令行/docker
  - 开发/云原生/Docker
  - 开发/容器化/Docker
  - 开发/容器化/containerd
  - 开发/云原生/containerd
  - 计算机/操作系统/Linux/内核/cgroups
---

# 在容器内如何列出正在被监听的端口或者打开的文件？

netstat - Docker: any way to list open sockets inside a running docker container? - Stack Overflow
https://stackoverflow.com/questions/40350456/docker-any-way-to-list-open-sockets-inside-a-running-docker-container

How to get listening ports inside a container without the netstat or lsof command | by <PERSON> | <PERSON><PERSON>
https://webera.blog/how-to-get-listening-ports-inside-a-container-without-the-netstat-or-lsof-command-83e21c772343

Netstat without Netstat inside Containers - DEV Community
https://dev.to/trexinc/netstat-without-netstat-inside-containers-9ak

netstat without netstat | Staaldraad
https://staaldraad.github.io/2017/12/20/netstat-without-netstat/
