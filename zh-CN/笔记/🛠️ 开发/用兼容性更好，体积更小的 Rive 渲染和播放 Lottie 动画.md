---
tags:
  - 开发/前端/动画
  - 艺术/设计/动画/前端/Lottie
  - 艺术/设计/动画/前端/Rive
  - 艺术/设计/动画/Telegram/Lottie-TGS
  - 开发/前端/动画/Lottie
  - 开发/前端/动画/Rive
---

# 用兼容性更好，体积更小的 Rive 渲染和播放 Lottie 动画

grammY
https://grammy.dev/

website/site/docs/README.md at main · grammyjs/website
https://github.com/grammyjs/website/blob/fe034a9c06499ca2db3947642f58bd0915ae0faa/site/docs/README.md?plain=1#L30-L38

PonomareVlad/lazy-lottie-player
https://github.com/PonomareVlad/lazy-lottie-player

LottieFiles/lottie-player: Lottie viewer/player as an easy to use web component! https://lottiefiles.com/web-player
https://github.com/LottieFiles/lottie-player?tab=readme-ov-file

airbnb/lottie-web: Render After Effects animations natively on Web, Android and iOS, and React Native. http://airbnb.io/lottie/
https://github.com/airbnb/lottie-web

Lottie Web Player - LottieFiles
https://lottiefiles.com/web-player

@dotlottie/player-component - npm
https://www.npmjs.com/package/@dotlottie/player-component

How to use library with Web Components in Vitepress - Stack Overflow
https://stackoverflow.com/questions/77397316/how-to-use-library-with-web-components-in-vitepress

Vitepress cannot build custom elements (web components) · Issue #2367 · vuejs/vitepress
https://github.com/vuejs/vitepress/issues/2367

SSR Compatibility | VitePress
https://vitepress.dev/guide/ssr-compat#clientonly

Rive vs Lottie
https://rive.app/blog/rive-as-a-lottie-alternative

Rive vs Lottie (and Other) Animations | by Abe Yang | All By Design | Medium
https://medium.com/all-by-design/rive-vs-lottie-and-other-animations-3a992589362b

Rive - Build interactive motion graphics that run anywhere
https://rive.app/

How to import your Lottie files into Rive
https://rive.app/blog/how-to-import-your-lottie-files-into-rive

Lottie/TGS Editor
https://michielp1807.github.io/lottie-editor/#/editor
