---
tags:
  - 开发/前端
  - 设计/UI/Refactoring UI
  - 笔记/读书笔记
  - 书籍/Refactoring UI
---

# Refactoring UI 读书笔记

## 如何从零开始

### 从功能开始，而不是布局

> Start with a feature, not a layout

先实现功能，然后再考虑布局。对于简单的功能，你甚至可能不需要导航栏、页脚或侧边栏。

### 以循环的方式工作

> Work in cycles

> Instead of designing everything up front, work in short cycles. Start by designing a simple version of the next feature you want to build.

与其事先设计好一切，不如在短周期内完成工作。从设计下一个要构建的功能的简单版本开始。

### 做一个悲观主义者

> Be a pessimist
>
> Don’t imply functionality in your designs that you aren’t ready to build.

当你在设计一个新功能时，预计它的构建会很困难。设计你能发布的最小有用版本可以大大降低这种风险。

> When you’re designing a new feature, expect it to be hard to build. Designing the smallest useful version you can ship reduces that risk considerably.
>
> If part of a feature is a “nice-to-have”, design it later. Build the simple version first and you’ll always have something to fall back on.

如果功能的一部分是“锦上添花”的，那么稍后再设计它。首先构建简单版本，这样你总会有东西可以依靠。

## 层次结构就是一切

> Hierarchy is Everything

> Not all elements are equal

并非所有元素都是平等的

### 语义是次要的

> Semantics are secondary

语义是按钮设计的重要组成部分，但这并不意味着你可以忘记层次结构。

页面上的每个操作都位于重要性金字塔中的某个位置。大多数页面只有一个真正的主要操作、几个不太重要的次要操作以及一些很少使用的第三级操作。

PRIMARY ｜SECONDARY｜TERTIARY

•主要行动应该是显而易见的。固体、高对比度的背景颜色在这里效果很好。
•次要行动应该明确，但不突出。轮廓样式或较低的对比度背景颜色是很好的选择。
•第三级按钮应该是可发现的，但不显眼的。像链接一样对这些操作进行造型通常是最好的方法。

如果破坏性操作不是页面上的主要操作，则最好使用二级或三级按钮。

### 不要在彩色背景上使用灰色文字

> Don’t use grey text on colored backgrounds
>
> A better approach is to hand-pick a new color, based on the background color.
> Choose a color with the same hue, and adjust the saturation and lightness until it looks right to you.

更好的方法是根据背景色手工挑选新的颜色。

选择相同色调的颜色，然后调整饱和度和明度，直到你觉得合适为止。

### 标签是最后的手段

> Labels are a last resort

## 布局和间距

> Layout and Spacing

使设计变清晰的最简单方法之一就是给每个元素多一点呼吸的空间。

### 应删除而不是添加空白

更好的方法是首先给一些东西太多的空间，然后将其删除，直到你对结果感到满意为止。

### 密集的用户界面有他们的位置

例如，如果您正在设计某种仪表板，其中需要同时显示大量信息。重要的是让这成为一个深思熟虑的决定，而不仅仅是默认。当您需要删除空格时，比需要添加空格时要明显得多。


### 缩小画布

如果您很难在大画布上设计一个小界面，请缩小画布！很多时候，当限制是真实的时，设计一些小东西更容易。

如果您正在构建一个响应式Web应用程序，请尝试从~400px的画布开始，并先设计移动布局。

一旦你有一个你满意的移动设计，把它带到一个更大尺寸的屏幕上，并在更小的屏幕上调整任何感觉像妥协的东西。很有可能你不必像你想象的那样改变。

### 相对尺寸不可缩放

作为一般规则，大屏幕上较大的元素需要比已经相当小的元素缩小得更快——在小屏幕尺寸下，小元素与大元素之间的差异应该不那么极端。

### 避免模糊的间距

> Avoid ambiguous spacing

每当你依靠间距来连接一组元素时，总是确保围绕这组元素的空间比其内部的空间更大——难以理解的界面看起来总是更糟糕。

## 设计文本

> Designing Text

### 忽略那些少于五种字重的字体

这并不总是对的，但作为一个一般规则，拥有很多不同字重的字体往往比那些只有较少字重的字体制作得更加细致和用心。

许多字体目录（如谷歌字体）允许你通过“样式数量”来过滤，这是可用字重以及这些字重的斜体变化组合而成。

一个限制你必须从中选择的选项数量的好方法是将其提高到10+（考虑到斜体）：

特别是在 Google Fonts 上，这会删除 85% 的可用选项，只剩下不到 50 个无衬线字体可供选择。

#### 🤔 疑问

这种方法对中文字体同样有效吗？


### 优化可读性

避免在主 UI 文本中使用 x 高度较矮的压缩字体。

### 相信群众的智慧

利用成千上万其他人的集体决策权可以使事情变得容易得多。比如字体排行榜，热门排序等等

检查一些你最喜欢的网站，看看他们正在使用什么字体。

### 保持你的行长度适中

为了获得最佳阅读体验，请确保段落足够宽，每行可容纳 45 到 75 个字符。在 Web 上执行此操作的最简单方法是使用 `em` 单位，它与当前字体大小相关。 `20-35em` 的宽度将让您处于正确的范围。

### 处理更宽的内容

将段落文本与图像或其他大型组件混合时，即使整体内容区域需要更宽以容纳其他元素，仍应限制段落宽度。结果总是看起来更加精致。

### 行高是成比例的

#### 考虑行的长度

当文本行过长时，问题会变得更加明显。阅读下一行时，眼睛需要水平移动的距离越远，就越容易丢失当前位置。
这意味着行高和段落宽度应成正比——窄的内容可以采用较短的行高，例如1.5；而宽的内容则可能需要将行高调整至2。
但是，随着文本尺寸增大，眼睛对帮助的需求减少。因此，对于大号标题文本来说，可能不需要额外增加行间距，1倍的行高已经足够。

### 可读性

#### 不要居中长文本

居中排版适用于标题或简短、单独的文本段落。但对于超过两三行的文本，左对齐会更为合适。
如果您想要将几段文本居中，但发现其中一段过长，最佳解决方案是重新编写这部分内容，使其变得更加简洁。这样不仅能解决对齐问题，还能让设计看起来更加协调统一。

## 颜色的使用

### 摒弃十六进制 (hex)，采用 HSL

十六进制和 RGB 是网络上最常用的颜色表示格式，但它们并不是最有用的。
使用十六进制或 RGB，视觉上有很多共同点的颜色在代码中看起来却完全不一样。
HSL通过使用人眼直观感知的属性来表示颜色来解决这个问题：色调、饱和度和亮度。

#### HSL 与 HSB

不要混淆 HSL 和 HSB - HSL 中的亮度与 HSB 中的亮度不同。
在 HSB 中，0% 的亮度总是黑色的，而 100% 的亮度只有在饱和度为 0% 时才是白色的。当饱和度为 100% 时，HSB 中的 100% 亮度与 HSL 中的 100% 饱和度和 50% 亮度相同。

### 你需要的颜色比你想象的要多

你是否使用过调色板生成器？你可以选择一种起始颜色，然后调整一些选项，最后就会得到用于构建网站的五种完美颜色？但是如果你真的把这五种颜色用在你的网站上，因为鲜艳的配色，网站会看起来像儿童玩具积木。

只有五个十六进制代码是无法构建任何东西的。要建造真正的东西，你需要一套更全面的颜色供你选择。

#### 灰色 (Grays | Neutral)

文本、背景、面板、表单控件--界面中几乎所有的东西都是灰色的。

考虑到不同的字重，不同的背景你可能需要 8-10 种灰色。

#### 主题色 (Primary)

就像灰色一样，你需要有多种（5-10 种）包括浅色和深色的主题色。

#### 点缀色 (Accents)

除主色调外，每个网站还需要一些强调色来向用户传达不同的信息。例如:

1. 黄色、粉色或茶色等吸引眼球的颜色来突出新功能
2. 红色表示确认破坏性操作
3. 黄色用于警告消息
4. 绿色，以突出积极的趋势

虽然这些颜色在整个用户界面中应尽量少用，但也需要多种色调。


总而言之，一个复杂的用户界面需要多达十种不同的颜色，每种颜色有 5-10 种色调，这种情况并不少见。

### 预先确定您的色调

当需要在调色板中为某种颜色创造更浅或更深的变体时，避免直接使用 CSS 预处理器的函数（如“变亮”或“变暗”）来即兴生成色彩。这会导致出现多达35种略有差异但几乎相同的蓝色。

相反，应提前定义一套固定的色彩系列供日后选择使用。

#### 先选择基色

> 挑选一种适合作为按钮背景的色调。

首先为您要创建的色阶选择一个基色（中间的颜色），您的浅色和深色色调都要以它为基础。

这并没有真正科学的方法，但对于原色和重点色来说，一个好的经验法则是挑选一种**适合作为按钮背景的色调**。

需要注意的是，这里并没有 "从 50% 明度开始 "之类的真正规则--每种颜色的表现都有些不同，所以你必须依靠自己的眼睛来判断。

#### 寻找最浅和最深的颜色

接下来，选择你的最深色调和最浅色调。颜色的最深色通常保留用于文本，而最浅色可能用于给元素的背景着色。

一个简单的警告组件就是一个很好地结合了这两种用例的例子，因此它可以是挑选这些颜色的绝佳地方。

从与基本颜色色调相匹配的颜色开始，并调整饱和度和亮度，直到您满意为止。


#### 填补空白

有了底色、深色和浅色之后，就需要填补它们之间的空白。

对于大多数项目来说，每种颜色至少需要 5 个色调，如果不想太拘束，可能需要 10 个色调。

9 是一个很好的数字，因为它很容易分割，而且填色也更简单。我们把最深的色调称为 900，基本色调称为 500，最浅的色调称为 100。

首先选择色调 700 和 300，也就是位于空白处中间的色调。你希望这两种色调是两边色调的完美折衷。

这样，色阶中就多了四个空隙（800、600、400 和 200），你可以用同样的方法来填补它们。

您最终应该获得一套相当平衡的颜色，这些颜色提供足够的选项来适应您的设计理念，而不会感到局限。


#### 灰色

对于灰色，底色并不那么重要，但过程是一样的。

通过为项目中最深的文本选择颜色来选择最深的灰色，通过选择适合微妙灰白色背景的颜色来选择最浅的灰色。

#### 调色板不是一门科学

尽管很诱人，但你不能纯粹依靠数学来制作完美的调色板。

像上面描述的系统化方法可以帮助你入门，但不要害怕在需要时做一些小调整。

一旦你开始在设计中使用你的颜色，你几乎不可避免地会想要调整某个色调的饱和度，或者让几个色调更亮或更暗。相信你的眼睛，而不是数字。

如果可以的话，尽量避免频繁添加新的色调。如果你不努力限制你的调色板，那还不如没有色彩系统。



### 不要让亮度扼杀饱和度

在 HSL 色彩空间中，当颜色的亮度接近 0% 或 100% 时，饱和度的影响就会减弱--同样的饱和度值，亮度为 50% 时比亮度为 90% 时看起来更艳丽。

这就意味着，如果你不想让给定颜色的浅色和深色色调看起来冲淡，就需要在亮度远离 50% 时增加饱和度。

虽然这很微妙，但像这样的小细节会让效果更加明显，尤其是当一种颜色应用于用户界面的大部分区域时。

但是，如果您的基色饱和度已经很高，该怎么办呢？如果饱和度已经达到 100%，如何提高饱和度？

#### 颜色的感知亮度

尽管黄色和海蓝色在 HSL 色彩模型中具有相同的“亮度”值，但我们通常会觉得黄色看起来更为明亮。这是因为不同的颜色调在人眼中的感知亮度是不同的。

通过使用特定的公式计算颜色的RGB分量，我们可以得到不同颜色的感知亮度值。例如，在饱和度为100%，亮度为50%的条件下，黄色的感知亮度高于蓝色。

值得注意的是，感知亮度并不是线性变化的，而是呈现出三个局部最小值（红色、绿色、蓝色）和三个局部最大值（黄色、青色、品红色）。这些信息有助于我们更好地理解和使用颜色。

#### 通过旋转色调改变亮度

由于不同色调的亮度不同，改变颜色亮度的另一种方法就是旋转色调。

要使颜色变浅，可将色调向最近的亮色调旋转（60°、180° 或 300°）。

要使颜色变暗，可将色调向最近的暗色调旋转（0°、120° 或 240°）。

色调的旋转角度不要超过 20-30°，否则颜色看起会完全不同。


### 灰色不一定是灰色

根据定义，真正的灰色饱和度为 0% - 它根本没有任何实际的颜色。

但实际上，很多我们认为的灰色实际上饱和度很高：

这种饱和度使得一些灰色给人冷色的感觉，而另一些灰色给人暖色的感觉。

#### 色温

如果你以前买过灯泡，就会在发出偏黄光的 "暖白 "灯泡和发出偏蓝光的 "冷白 "灯泡之间做出选择。

用户界面中的灰色饱和也是类似的道理。

如果你想让你的灰色给人一种冷色调的感觉，那就在灰色中加入一点蓝色：

如果想让灰色给人温暖的感觉，可以在灰色中加入一点黄色或橙色：

为了保持一致的温度，不要忘记提高浅色和深色色调的饱和度。否则，与亮度接近 50% 的灰色相比，这些色调会显得有些暗淡。

至于灰阶的饱和度如何，完全取决于你自己--如果你只想让温度略微偏高，就只增加一点；如果你想让界面向一个或另一个方向强烈倾斜，就把饱和度调高。


### 无障碍并不一定意味着丑陋

为了确保您的设计是无障碍的，《网页内容可访问性指南》（WCAG）建议普通文本（约 18px 以下）的对比度至少为 `4.5:1`，较大文本的对比度至少为 `3:1`。

对于典型的浅色背景上的深色文字，满足这一建议非常容易，但如果开始使用颜色，就会变得非常棘手。

#### 翻转对比：彩色背景上的白色文本

在彩色背景上使用白色文本时，您会惊讶于颜色通常需要有多深才能满足 `4.5:1` 的对比度。

您可以通过翻转对比度来解决这个问题。与其在深色背景上使用浅色文字，不如在浅色背景上使用深色文字

颜色仍然有助于衬托文字，但却不会那么突兀，也不会干扰页面上的其他操作。

#### 旋转色调：彩色背景上的彩色文字

比彩色背景上的白色文字更难的是彩色背景上的彩色文字。如果你想为深色面板中的辅助文字选择颜色，就会遇到这种情况。

您不希望主要文本和次要文本看起来一样，那么您还能做些什么呢？

因为有些颜色比其他颜色更亮，所以在不接近白色的情况下增加对比度的一种方法是将色调转向更亮的颜色，如青色、洋红色或黄色。

这样既能让文字更容易看懂，又能保持其丰富多彩。


### 不要只依赖颜色

色彩是增强信息效果并使其更易于理解的绝佳方式，但要注意不要依赖它，否则色盲用户将很难理解你的用户界面。

解决这个问题的一个简单办法就是用其他方式来传达信息，比如添加图标来表示变化是正还是负。

可以尝试使用对比度不同的颜色，这样色盲的人就更容易分辨出来。对他们来说，明暗之间的区别比两种不同颜色之间的区别还要大。

一定要使用色彩来辅助你的设计所表达的内容，而不要将其作为唯一的交流手段。
