# 超出文本显示省略号

## 常规

```css
display: -webkit-box;
-webkit-box-orient: vertical;
-webkit-line-clamp: 1;
overflow: hidden;
word-break: break-all;
```

修改 `-webkit-line-clamp` 数值来指定超过多少行进行换行。

## 避免折断英文单词

同时避免链接之类的无分割长英文溢出。

```css
display: -webkit-box;
white-space: pre-wrap;
overflow-wrap: break-word;
word-wrap: break-word;
-ms-word-break: break-all;
word-break: break-word;
overflow: hidden;
-webkit-line-clamp: 20;
-webkit-box-orient: vertical;
```

修改 `-webkit-line-clamp` 数值来指定超过多少行进行换行。

## 禁止换行

```css
white-space: nowrap;
```
