---
status: 尚未完成
---
# 学习资料

[文件增量同步之rsync算法 - 掘金](https://juejin.cn/post/6865562336158023688)

[基于rsync的文件增量同步方案 - 美团技术团队](https://tech.meituan.com/2017/04/27/incre-sync-use-rsync.html)

[如何实现文件增量同步——算法-阿里云开发者社区](https://developer.aliyun.com/article/576404)

[谈谈文件增量同步算法:RSYNC和CDC_mob604756f2dcb4的技术博客_51CTO博客](https://blog.51cto.com/u_15127580/4335441)

[实现一个多人协作在线文档有哪些技术难点？ - 知乎](https://www.zhihu.com/question/274573543)

[ether/etherpad-lite: Etherpad: A modern really-real-time collaborative document editor.](https://github.com/ether/etherpad-lite)

[实时协同编辑的实现 - FEX](https://fex.baidu.com/blog/2014/04/realtime-collaboration/)

[如何实现多人协作的在线文档 - 掘金](https://juejin.cn/post/6992800872463859743)

[协同文档的技术实现 - 腾讯Web前端 IMWeb 团队社区 | blog | 团队博客](https://imweb.io/topic/5b3ec8dd4d378e703a4f4450)

[钉钉文档协同编辑背后的核心技术原理-阿里云开发者社区](https://developer.aliyun.com/article/738238)

[Ace - The High Performance Code Editor for the Web](https://ace.c9.io/)

[Wave | Real-time collaboration and coediting service](https://wave.codox.io/)

[adler32 package - hash/adler32 - pkg.go.dev](https://pkg.go.dev/hash/adler32)

[package adler32 | Go 标准库 中文参考](https://wizardforcel.gitbooks.io/golang-stdlib-ref/content/62.html)
