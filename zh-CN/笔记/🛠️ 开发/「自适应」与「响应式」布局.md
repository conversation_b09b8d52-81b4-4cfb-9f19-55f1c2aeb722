---
tags:
  - 开发/前端
  - 艺术/设计
---

# 三分钟了解「自适应」与「响应式」布局[🔗](https://zhuanlan.zhihu.com/p/157443159)

自适应与响应式布局，别再傻傻分不清，本文作者从以下几点详细介绍响应式和自适应有什么区别。

## 自适应布局

自适应布局是网页内容根据设备的不同而进行适应；

通过检测视口分辨率，来判断当前访问的设备是pc端、平板还是手机，从而请求服务层，返回不同的页面；

需要根据不同使用场景开发多套界面。

![](https://pic4.zhimg.com/80/v2-3520dfbf13d5a7af8258ff443eab7737_720w.jpg)

### 技术原理

分别为不同的屏幕分辨率定义布局，即创建多个静态布局，每个静态布局对应一个屏幕分辨率范围。改变屏幕分辨率可以切换不同的静态局部（页面元素位置发生改变），但在每个静态布局中，页面元素不随窗口大小的调整发生变化。

**布局特点：**屏幕分辨率变化时，页面里面元素的位置会变化，而大小不会变化。

**设计方法：**使用媒体查询给不同尺寸和介质的设备切换不同的样式，在同一个设备下实际还是固定的布局。

## 响应式布局

响应式布局是网页的布局针对屏幕大小的尺寸而进行响应；

通过检测视口分辨率，针对不同客户端在客户端做代码处理，来展现不同的布局和内容；

只需要开发一套界面即可适用于所有尺寸及终端。

![](https://pic1.zhimg.com/80/v2-a88758f587c4f292b2f69e043ad1ebb4_720w.jpg)

### 技术原理

糅合了流式布局+弹性布局，再搭配媒体查询技术使用。

分别为不同的屏幕分辨率定义布局，同时，在每个布局中，应用流式布局的理念，使页面元素宽度随着窗口调整而自动适配。

即：创建多个流体式布局，分别对应一个屏幕分辨率范围。可以把响应式布局看作是流式布局和自适应布局设计理念的融合。

**布局特点：**每个屏幕分辨率下面会有一个布局样式，即元素位置和大小都会变。

**设计方法：**媒体查询+流式布局。

通常使用媒体查询和网格系统配合相对布局单位进行布局，实际上就是综合响应式、流动等技术通过 CSS 给单一网页不同设备返回不同样式的技术统称。
