## 通用的插件接口
> 每当这个插件被添加到应用程序中时，如果它是一个对象，就会调用 `install` 方法。如果它是一个 `function`，则函数本身将被调用。在这两种情况下——它都会收到两个参数：由 Vue 的 `createApp` 生成的 `app` 对象和用户传入的选项。
> 让我们从设置插件对象开始。建议在单独的文件中创建它并将其导出，如下所示，以保持包含的逻辑和分离的逻辑。

```JavaScript
import type { App } from 'vue' // 导入类型
import MyComponent from './index.vue' // 导入同目录下的组件

export default {
  install: (app: App) => { // vue 插件通用接口，必须要暴露一个 install 方法
    app.component(MyComponent.tag, MyComponent) // 注入的代码，此处是构建组件
  },
}
```

## 构建组件

创建一个 Vue 文件，写入以下的内容：

```html
<template>
  <div class="v-component">
    <slot></slot>
  </div>
</template>

<script lang="ts">
```
```typescript
export default defineComponent({
  tag: 'v-component-tag', // 组件的标签，使用的时候可以作为 <v-component-tag> 来调用
  name: 'VComponent', // 组件的名字
  props: {
    classname: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const classes = ref(props.classname) // 获取传参
    return {
      classes,
      styles,
    }
  },
})
```
```html
</script>
```

创建一个 TS 文件，生命我们的插件：

```typescript
import type { App } from 'vue'
import VComponent from './index.vue'

export default {
  install: (app: App) => {
    app.component(VComponent.tag, VComponent)
  },
}
```

现在就完成了，接下来可以去 src/main.ts 文件导入我们的插件：

```typescript
import Vue from 'vue'
import VComponent from '../components/VComponent' // 导入

const app = Vue.createApp({})
app.use(VComponent) // 启用
```

如果使用的是 ViteSSG，可以这样导入插件：

```typescript
// register vue composition api globally
import { ViteSSG } from 'vite-ssg'
import generatedRoutes from 'virtual:generated-pages'
import { setupLayouts } from 'virtual:generated-layouts'
import VComponent from '../components/VComponent' // 导入

import App from './App.vue'
export const createApp = ViteSSG(
  App,
  { routes },
  (ctx) => {
    ctx.app.use(VComponent) // 启用插件
    // install all modules under `modules/`
    Object.values(import.meta.globEager('./modules/*.ts')).map(i => i.install?.(ctx))
  },
)
```



## 参考

[应用 API - Vue3 官方文档](https://v3.cn.vuejs.org/api/application-api.html#directive)
[编写插件 - Vue3 官方文档](https://v3.cn.vuejs.org/guide/plugins.html#%E7%BC%96%E5%86%99%E6%8F%92%E4%BB%B6)