# 🐚 Vite2 搭配 Vue3 模版项目
-   ⚡️ [Vue3.2](https://github.com/vuejs/vue-next)，[Vite2](https://github.com/vitejs/vite)，[PNPM](https://pnpm.js.org/)
-   🗂 [基于文件的路由](https://github.com/antfu/vitesse/blob/main/src/pages)
-   🍍 Vuex 或 Pinia
-   📑 layouts 主题系统
-   📲 [PWA](https://github.com/antfu/vite-plugin-pwa)
-   🎨 [Windi CSS](https://github.com/windicss/windicss) CSS 工具类框架
-   🌍 [I18n](https://github.com/antfu/vitesse/blob/main/locales)
-   🖨 服务端渲染 SSG
-   TypeScript
-   SVG 雪碧图支持

## 低优先级

- 📥 [API 自动导入](https://github.com/antfu/unplugin-auto-import)- 直接使用 Composition API 和其他 API
- 📦 [组件自动导入](https://github.com/antfu/vitesse/blob/main/src/components)
- ⚙️在[GitHub Actions](https://github.com/features/actions)上使用[Cypress](https://cypress.io/)进行E2E 测试[](https://github.com/features/actions)
- 🗒 [降价支持](https://github.com/antfu/vite-plugin-md)
- 😃 [使用任何图标集中的图标，毫不妥协](https://github.com/antfu/unplugin-icons)



## 附加

- RESTful 规范的 TypeScript 封装

- ESlint 规范
- 完成 i18n 基础配置（自动识别系统语言，持久化）
- 暗色模式基础配置 （自动识别系统主题色，持久化）
- TSX 支持
- SVGO 支持

