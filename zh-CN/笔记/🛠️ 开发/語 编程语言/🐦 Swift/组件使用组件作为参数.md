[ios - How do you create a SwiftUI view that takes an optional secondary View argument? - Stack Overflow](https://stackoverflow.com/questions/58384580/how-do-you-create-a-swiftui-view-that-takes-an-optional-secondary-view-argument)

[Pass SwiftUI view as argument to another view](https://programmingwithswift.com/pass-swiftui-view-as-argument-to-another-view/)

[Creating Optional @ViewBuilder Parameters in SwiftUI Views | by <PERSON> | Medium](https://michael-ginn.medium.com/creating-optional-viewbuilder-parameters-in-swiftui-views-a0d4e3e1a0ae)
