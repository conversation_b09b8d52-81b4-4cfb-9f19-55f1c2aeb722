[MacOS开发系列1-使用Accessibility API对所有程序的监控和控制 - 掘金](https://juejin.cn/post/6919528826196197390#heading-6)

[Mac开发系列3-通过AXError来判断Accessibility API的调用情况 - 掘金](https://juejin.cn/post/6919737191388479495)

[macos - How to retrieve active window URL using Mac OS X accessibility API - Stack Overflow](https://stackoverflow.com/questions/53229924/how-to-retrieve-active-window-url-using-mac-os-x-accessibility-api)

[How can I use Cocoa's Accessibility API to detect that a window is brought to front? - Stack Overflow](https://stackoverflow.com/questions/347396/how-can-i-use-cocoas-accessibility-api-to-detect-that-a-window-is-brought-to-fr)

[ios - How to write Application Logs to File and get them - Stack Overflow](https://stackoverflow.com/questions/44537133/how-to-write-application-logs-to-file-and-get-them)

[macos - set the size and position of all windows on the screen in swift - Stack Overflow](https://stackoverflow.com/questions/47480873/set-the-size-and-position-of-all-windows-on-the-screen-in-swift)

[objective c - Window list from AXUIElementRef always null on High Sierra - Stack Overflow](https://stackoverflow.com/questions/49972254/window-list-from-axuielementref-always-null-on-high-sierra)

[objective c - Move other windows on Mac OS X using Accessibility API - Stack Overflow](https://stackoverflow.com/questions/21069066/move-other-windows-on-mac-os-x-using-accessibility-api)

## 权限

[Requesting Access to Protected Resources | Apple Developer Documentation](https://developer.apple.com/documentation/uikit/protecting_the_user_s_privacy/requesting_access_to_protected_resources)

## 测试和说明

[Accessibility Programming Guide for OS X: The OS X Accessibility Model](https://developer.apple.com/library/archive/documentation/Accessibility/Conceptual/AccessibilityMacOSX/OSXAXmodel.html)

[Accessibility Programming Guide for OS X: Testing for Accessibility on OS X](https://developer.apple.com/library/archive/documentation/Accessibility/Conceptual/AccessibilityMacOSX/OSXAXTestingApps.html#//apple_ref/doc/uid/TP40001078-CH210-TPXREF101)

## 相关阅读

[CGWindowListCopyWindowInfo(_:_:) | Apple Developer Documentation](https://developer.apple.com/documentation/coregraphics/1455137-cgwindowlistcopywindowinfo/)
