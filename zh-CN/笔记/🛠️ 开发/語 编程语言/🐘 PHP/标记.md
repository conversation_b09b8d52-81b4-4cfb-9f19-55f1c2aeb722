# PHP 标记

PHP 代码被包含在特殊的[起始符和结束符 :`<?php`,`?>`](https://www.php.net/manual/zh/language.basic-syntax.phpmode.php) 中，使得可以进出“PHP 模式”。

```php
<html>
  <head>
    <title>Example</title>
  </head>
  <body>

​    <?php
​    	echo "Hi, I'm a PHP script!";
​    ?>

  </body>
</html>
```

## 开始和结束标记

PHP 有一个 echo 标记简写 `<?=`， 它是更完整的 `<?php echo` 的简写形式。

> 短标记 (第三个例子) 是被默认开启的，但是也可以通过 [short_open_tag](https://www.php.net/manual/zh/ini.core.php#ini.short-open-tag) php.ini 来直接禁用。如果 PHP 在被安装时使用了 **--disable-short-tags** 的配置，该功能则是被默认禁用的。

```php
<?php echo 'if you want to serve PHP code in XHTML or XML documents,
	use these tags'; ?>

<?= 'print this string' ?>

<? echo 'this code is within short tags, but will only work '.
	'if short_open_tag is enabled'; ?>
```

如果文件内容仅仅包含 PHP 代码，最好在文件末尾删除 PHP 结束标记。可以避免 PHP 输出 结束符后面的空格或者回车。

```PHP
<?php
echo "Hello world";

```

## 跳过 HTML（if）

大多数情况下当 PHP 解释器碰到 `?>` 结束标记时就简单地将其后内容原样输出，例外是处于条件语句中间时。

下面的例子中 PHP 将在输出 HTML 时跳过条件语句未达成的段落，即使该段落位于 PHP 开始和结束标记之外。

```php
<?php if ($expression == true): ?>
  This will show if the expression is true.
<?php else: ?>
  Otherwise this will show.
<?php endif; ?>
```
