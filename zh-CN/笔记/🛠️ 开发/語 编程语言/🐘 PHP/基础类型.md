# 基础类型

### 目錄

[toc]

## Boolean 布尔类型

要指定一个布尔值，使用常量 **`true`** 或 **`false`**。两个都不区分大小写。

```PHP
$foo = True; // 设置 $foo 为 TRUE
```

当转换为 boolean 时，以下值被认为是 **`false`**：

- [布尔](https://www.php.net/manual/zh/language.types.boolean.php)值 **`false`** 本身
- [整型](https://www.php.net/manual/zh/language.types.integer.php)值 0（零）及 -0 (零)
- [浮点型](https://www.php.net/manual/zh/language.types.float.php)值 0.0（零）-0.0(零)
- 空[字符串](https://www.php.net/manual/zh/language.types.string.php)，以及[字符串](https://www.php.net/manual/zh/language.types.string.php) "0"
- 不包括任何元素的[数组](https://www.php.net/manual/zh/language.types.array.php)
- 特殊类型 [NULL](https://www.php.net/manual/zh/language.types.null.php)（包括尚未赋值的变量）
- 从空标记生成的 [SimpleXML](https://www.php.net/manual/zh/ref.simplexml.php) 对象

所有其它值都被认为是 **`true`**（包括任何[资源](https://www.php.net/manual/zh/language.types.resource.php) 和 **`NAN`**）。

## Integer 整型

整型值 int 可以使用十进制，十六进制，八进制或二进制表示

> 从 PHP 7.4.0 开始，整型数值可能会包含下划线 (`_`)，为了更好的阅读体验，这些下划线在展示的时候，会被 PHP 过滤掉。

```php
<?php
$a = 1234; // 十进制数
$a = 0123; // 八进制数，前缀 0
$a = 0x1A; // 十六进制数，前缀 0x
$a = 0b11111111; // 二进制数,前缀 0b
$a = 1_234_567; // 整型数值 (PHP 7.4.0 以后)
?>
```

整型数 int 的字长和平台有关，尽管通常最大值是大约二十亿（32 位有符号）。64 位平台下的最大值通常是大约 9E18。 PHP 不支持无符号的 int。int 值的字长可以用常量 **`PHP_INT_SIZE`**来表示， 最大值可以用常量 **`PHP_INT_MAX`** 来表示， 最小值可以用常量 **`PHP_INT_MIN`** 表示。

#### 整型溢出

如果给定的一个数超出了 int 的范围，将会被解释为 float。同样如果执行的运算结果超出了 int 范围，也会返回 float。

## Float 浮点数

```php
<?php
$a = 1.234;
$b = 1.2e3;
$c = 7E-10;
$d = 1_234.567; // 从 PHP 7.4.0 开始支持
?>
```

### 浮点数的精度

浮点数的精度有限。PHP 通常使用 IEEE 754 双精度格式，则由于取整而导致的最大相对误差为 1.11e-16。

例如，`floor((0.1+0.7)*10)` 通常会返回 `7` 而不是预期中的 `8`，因为该结果内部的表示其实是类似 `7.9999999999999991118...`。

所以永远不要相信浮点数结果精确到了最后一位，也永远不要比较两个浮点数是否相等。如果确实需要更高的精度，应该使用[任意精度数学函数](https://www.php.net/manual/zh/ref.bc.php)或者 [gmp 函数](https://www.php.net/manual/zh/ref.gmp.php)。

### 比较浮点数

如上述警告信息所言，由于内部表达方式的原因，比较两个浮点数是否相等是有问题的。不过还是有迂回的方法来比较浮点数值的。

要测试浮点数是否相等，要使用一个仅比该数值大一丁点的最小误差值。该值也被称为机器极小值（epsilon）或最小单元取整数，是计算中所能接受的最小的差别值。

`$a` 和 `$b` 在小数点后五位精度内都是相等的。

```php
<?php
$a = 1.23456789;
$b = 1.23456780;
$epsilon = 0.00001;

if(abs($a-$b) < $epsilon) {
    echo "true";
}
?>
```

### NaN

某些数学运算会产生一个由常量 **`NAN`** 所代表的结果。由于 **`NAN`** 代表着任何不同值，不应拿 **`NAN`** 去和其它值进行比较，包括其自身，应该用 [is_nan()](https://www.php.net/manual/zh/function.is-nan.php) 来检查。
