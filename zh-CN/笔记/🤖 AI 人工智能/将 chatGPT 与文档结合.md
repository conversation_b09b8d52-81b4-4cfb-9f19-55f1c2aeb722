---
status: 尚未完成
---
# 将 chatGPT 与文档结合

> [!WARNING]
> ⚠️ 该文档尚未完成，仍在编写中...

## 参考资料

[Chat with Document(s) using OpenAI ChatGPT API and Text Embedding | by <PERSON> | Mar, 2023 | Dev Genius](https://blog.devgenius.io/chat-with-document-s-using-openai-chatgpt-api-and-text-embedding-6a0ce3dc8bc8)

[Storing OpenAI embeddings in Postgres with pgvector](https://supabase.com/blog/openai-embeddings-postgres-vector)

[🦜🔗 LangChain 0.0.120](https://langchain.readthedocs.io/en/latest/reference.html)

[LlamaIndex documentation](https://gpt-index.readthedocs.io/en/latest/index.html)

[LangChain：Model as a Service粘合剂，被ChatGPT插件干掉了吗？](https://mp.weixin.qq.com/s/3coFhAdzr40tozn8f9Dc-w)

[GPT-4 ，人类迈向AGI的第一步（上半）](https://orangeblog.notion.site/GPT-4-AGI-8fc50010291d47efb92cbbd668c8c893)

[拆解追溯 GPT-3.5 各项能力的起源](https://yaofu.notion.site/GPT-3-5-360081d91ec245f29029d37b54573756#5a1bff82a11042a58871ed9dfa6e98c5)

## 参考项目

[hwchase17/notion-qa](https://github.com/hwchase17/notion-qa)

[JimmyLv/BibiGPT: BibiGPT 音视频内容一键总结](https://github.com/JimmyLv/BibiGPT)

[milvus-io/milvus: A cloud-native vector database with high-performance and high scalability.](https://github.com/milvus-io/milvus)

[pgvector/pgvector: Open-source vector similarity search for Postgres](https://github.com/pgvector/pgvector)

[weaviate/weaviate: Weaviate is an open source vector search engine that stores both objects and vectors](https://github.com/weaviate/weaviate)

[madawei2699/myGPTReader: myGPTReader is a slack bot that can read any webpage, ebook or document and summarize it with chatGPT.](https://github.com/madawei2699/myGPTReader)

[hwchase17/langchain: ⚡ Building applications with LLMs through composability ⚡](https://github.com/hwchase17/langchain)

[asg017/sqlite-vss: A SQLite extension for efficient vector search, based on Faiss!](https://github.com/asg017/sqlite-vss)
