---
tags:
  - 开发/语言/Rust
  - AI
  - 网站/GitHub
  - 分类/收集箱
  - 开源/社区
  - 开源/库
  - 开源/软件
---

# Rust 社区的 AI 生态

## burn

tracel-ai/burn: Burn is a new comprehensive dynamic Deep Learning Framework built using Rust with extreme flexibility, compute efficiency and portability as its primary goals.
https://github.com/tracel-ai/burn

## candle

huggingface/candle: Minimalist ML framework for Rust
https://github.com/huggingface/candle

## dfdx

coreylowman/dfdx: Deep learning in Rust, with shape checked tensors and neural networks
https://github.com/coreylowman/dfdx
