# 可用项目

## 神经网络实现

### nurpeiis/Steganography-using-GANs：使用生成模型生成隐写文本以克服审查和目标广告

[https://github.com/nurpeiis/Steganography-using-GANs](https://github.com/nurpeiis/Steganography-using-GANs)

#### 说明

##### ConcealGAN：序列数据的隐写术

该项目的主要目的是测试用于顺序隐写系统的双层编码以及 LeakGAN 和 RNN 模型。特别是，我们专注于生成隐写文本，作为序列数据的测试用例。当通过信使和电子邮件服务交换文本消息时，该项目可用于解决审查和定向广告问题。

#### 结论

没办法跑起来

### Greenleaf88/MDRSteg-large-capacity-image-steganography-based-on-multi-scale-dilated-ResNet-and-combined-chi-squ：论文源代码：《MDRSteg：基于多尺度dilated ResNet和组合卡方距离损失的大容量图像隐写术》

[https://github.com/Greenleaf88/MDRSteg-large-capacity-image-steganography-based-on-multi-scale-dilated-ResNet-and-combined-chi-squ](https://github.com/Greenleaf88/MDRSteg-large-capacity-image-steganography-based-on-multi-scale-dilated-ResNet-and-combined-chi-squ)

#### 说明

##### 实验总结

数据集: VOC2012
训练次数: 11540
测试次数: 5585

**注意**：测试集使用一半大小的图像覆盖另一半，使用相同的裁剪和调整大小方法

#### 结论

没办法跑起来

### whbwhb521/Spatial-Image-Steganography-Based-on-Generative-Adversarial-Network：基于生成对抗网络的空间图像隐写术

[https://github.com/whbwhb521/Spatial-Image-Steganography-Based-on-Generative-Adversarial-Network](https://github.com/whbwhb521/Spatial-Image-Steganography-Based-on-Generative-Adversarial-Network)

#### 说明

基于生成对抗网的空域图像隐写方案。论文：Spatial Image Steganography Based on Generative Adversarial Network的实现。 论文地址：[https://arxiv.org/abs/1804.07939](https://arxiv.org/abs/1804.07939) Path1是训练集目录 Path2为测试集目录 isTrain 是否为训练 generate 生成器 discrimation 判别器 Tanh 嵌入模拟器

#### 结论

没办法跑起来

### anilsathyan7/Deep-Video-Steganography-Hiding-Videos-in-Plain-Sight：深度视频隐写术 隐藏视频

[https://github.com/anilsathyan7/Deep-Video-Steganography-Hiding-Videos-in-Plain-Sight](https://github.com/anilsathyan7/Deep-Video-Steganography-Hiding-Videos-in-Plain-Sight)

#### 说明

##### 深度视频隐写术：隐藏视频

**用于将视频隐藏在其他视频**中的卷积神经网络。它使用**深度学习、隐写术和加密**的概念在**keras/tensorflow**中实现。

**隐写术是将秘密消息隐藏**在另一个普通消息中的做法。消息可以是图像、文本、视频、音频等。在现代隐写术中，目标是**秘密地传达**数字消息。隐写术的主要目的是防止检测到隐藏的消息。它通常与**密码学**结合以提高隐藏消息的安全性。**隐写分析**是使用隐写术（破解）检测隐藏信息的研究；这类似于应用于密码学的密码分析。隐写术用于机密通信、秘密数据存储、数字水印等**应用。**

#### 结论

没办法跑起来

### Marcovaldong/ISGAN: 生成对抗网络实现隐形隐写术的实现 (<https://arxiv.org/abs/1807.08571>)

[https://github.com/Marcovaldong/ISGAN](https://github.com/Marcovaldong/ISGAN)

#### 说明

“通过生成对抗网络实现隐形隐写术” ([https://arxiv.org/abs/1807.08571](https://arxiv.org/abs/1807.08571))

#### 结论

没办法跑起来

## 非神经网络实现

## DCT/DFT

[https://gist.github.com/bellbind/6fee86bd8027b57991f9](https://gist.github.com/bellbind/6fee86bd8027b57991f9)

[https://gist.github.com/bellbind/eb3419516e00fdfa13f472d82fd1b495](https://gist.github.com/bellbind/eb3419516e00fdfa13f472d82fd1b495)

## 空域到频域转换的盲水印

### 相关参考资料

[https://hsnico.github.io/2020/07/14/Misc-%E9%A2%91%E5%9F%9F%E7%9B%B2%E6%B0%B4%E5%8D%B0%E6%8A%97%E6%94%BB%E5%87%BB%E6%80%A7%E6%B5%8B%E8%AF%95/](https://hsnico.github.io/2020/07/14/Misc-%E9%A2%91%E5%9F%9F%E7%9B%B2%E6%B0%B4%E5%8D%B0%E6%8A%97%E6%94%BB%E5%87%BB%E6%80%A7%E6%B5%8B%E8%AF%95/)

### guofei9987/blind_watermark: Blind&Invisible Watermark （图片盲水印，提取水印无须原图！）

[https://github.com/guofei9987/blind_watermark](https://github.com/guofei9987/blind_watermark)

#### 结论

跑起来了，但是无法正常解析数据

fire-keeper/BlindWatermark: 使用盲水印保护创作者的知识产权using invisible watermark to protect creator's intellectual property

[](https://github.com/fire-keeper/BlindWatermark)

跑起来了，但是无法正常解析数据

### librauee/Steganalysis: 🦄Python 实现LSB算法进行信息隐藏 包含空域与变换域 JPEG信息隐藏算法 对PDF文件进行信息隐藏 基于卷积神经网络的隐写分析 Matlab SRM、SCA隐写分析

[https://github.com/librauee/Steganalysis](https://github.com/librauee/Steganalysis)

#### 结论

只是学习、参考资料

### c1y2m3/SimpleShellcode: 利用图片隐写术来远程动态加载shellcode

### 项目地址

<https://github.com/c1y2m3/SimpleShellcode>

#### 结论

只是学习、参考资料

#### 说明

将Shellcode隐写到正常BMP图片中，把字符串拆成字节，写入每个像素的alpha通道中，然后上传到可信任的网站下偏移拼接shellcode进行远程动态加载，能有效地增加了免杀性和隐匿性。

### auyer/steganography: Pure Golang Library that allows simple LSB steganography on images

[https://github.com/auyer/steganography](https://github.com/auyer/steganography)

#### 说明

steganography 是一个用纯 go 编写的库，允许对图像进行简单的 LSB 隐写术。它能够编码和解码图像。它可以存储任何格式的文件。这个库的灵感来自于 EthanWelsh 的 Stego，这是一个具有相同目的的命令行实用程序。

#### 结论

LSB 隐写十分容易被破解和识别

### EthanWelsh/Stego: 用 go 编写的命令行实用程序允许对 PNG 图像进行简单的 LSB 隐写术

[https://github.com/EthanWelsh/Stego](https://github.com/EthanWelsh/Stego)

#### 说明

用 go 编写的命令行实用程序允许对 PNG 图像进行简单的 LSB 隐写术

#### 结论

LSB 隐写十分容易被破解和识别

## 运行库和依赖

### tensorflow/tensorflow/go at master · tensorflow/tensorflow

[https://github.com/tensorflow/tensorflow/tree/master/tensorflow/go](https://github.com/tensorflow/tensorflow/tree/master/tensorflow/go)

#### 结论

只是学习、参考资料

### orktes/go-torch: LibTorch (PyTorch) bindings for Golang

[https://github.com/orktes/go-torch](https://github.com/orktes/go-torch)

#### 结论

只是学习、参考资料

## 延伸阅读

[不错的图像对抗生成网络相关内容 gitgiter/Graph-Adversarial-Learning: A curated collection of adversarial attack and defense on graph data.](https://github.com/gitgiter/Graph-Adversarial-Learning)
[阿里巴巴公司根据截图查到泄露信息的具体员工的技术是什么？ - 知乎](https://www.zhihu.com/question/50735753/answer/122593277)
[隐写术之图片隐写 - 知乎](https://zhuanlan.zhihu.com/p/62895080)
[【2019网络安全优秀创新成果展】高维数据 | 屏幕拍摄泄密溯源取证系统 - 中国网络安全产业联盟](http://www.china-cia.org.cn/home/<USER>
[利用图片隐写术来远程动态加载shellcode](https://mp.weixin.qq.com/s/QZ5YlRZN47zne7vCzvUpJw)
[C/C++信息隐写术（二）之字符串藏入BMP文件_IT1995的博客-CSDN博客](https://blog.csdn.net/qq78442761/article/details/54880328)
[loyalty-fox/idshwk7](https://github.com/loyalty-fox/idshwk7)
[LeeeLiu/STC_steg_GAN: 基于生成对抗网络的隐写](https://github.com/LeeeLiu/STC_steg_GAN)
[A DWT-SVD based Robust Digital Watermarking for Digital Images - ScienceDirect](https://www.sciencedirect.com/science/article/pii/S1877050918308081)
