---
tags:
  - AI
  - AI/教程
---

# 分布式训练

现在已经不推荐使用 `DataParallel` 了[^1]，

`rank`

`gpu`

`local_rank`：进程阶序（rank）

`global_rank`

`world_size`：总进程数

`torchrun`

`torch.distributed.launch`

`nproc_per_node`
## 什么是 DDP？

`DistributedDataParallel`

`NCCL`

`RDMA`

`NVML`

`torch.distributed`

分布式计算

All-Reduce

Reduced ring

> In CPython, the **global interpreter lock**, or **GIL**, is a mutex that protects access to Python objects, preventing multiple threads from executing Python bytecodes at once. The GIL prevents race conditions and ensures thread safety. A nice explanation of [how the Python GIL helps in these areas can be found here](https://python.land/python-concurrency/the-python-gil). In short, this mutex is necessary mainly because CPython's memory management is not thread-safe.
>
> 来源： [GlobalInterpreterLock - Python Wiki](https://wiki.python.org/moin/GlobalInterpreterLock)

Distributed training with 🤗 Accelerate
https://huggingface.co/docs/transformers/accelerate

[TorchX — PyTorch/TorchX main documentation](https://pytorch.org/torchx/latest/)

[pytorch/torchx: TorchX is a universal job launcher for PyTorch applications. TorchX is designed to have fast iteration time for training/research and support for E2E production ML pipelines when you're ready.](https://github.com/pytorch/torchx)

[TorchElastic Kubernetes — PyTorch 2.1 documentation](https://pytorch.org/docs/stable/elastic/kubernetes.html)

```shell
Initializing distributed: GLOBAL_RANK: 0, MEMBER: 1/3
----------------------------------------------------------------------------------------------------
distributed_backend=nccl
All distributed processes registered. Starting with 3 processes
----------------------------------------------------------------------------------------------------

LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]

  | Name     | Type    | Params
-------------------------------------
0 | conv1    | Conv2d  | 320
1 | conv2    | Conv2d  | 18.5 K
2 | dropout1 | Dropout | 0
3 | dropout2 | Dropout | 0
4 | fc1      | Linear  | 1.2 M
5 | fc2      | Linear  | 1.3 K
-------------------------------------
1.2 M     Trainable params
0         Non-trainable params
1.2 M     Total params
4.800     Total estimated model params size (MB)
```

```shell
Initializing distributed: GLOBAL_RANK: 1, MEMBER: 2/3
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]
```

```shell
Initializing distributed: GLOBAL_RANK: 2, MEMBER: 3/3
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0]
```

Pytorch DDP

[Getting Started with Distributed Data Parallel — PyTorch Tutorials 2.2.0+cu121 documentation](https://pytorch.org/tutorials/intermediate/ddp_tutorial.html)
[Writing Distributed Applications with PyTorch — PyTorch Tutorials 2.2.0+cu121 documentation](https://pytorch.org/tutorials/intermediate/dist_tuto.html)
[Saving and Loading Models — PyTorch Tutorials 2.2.0+cu121 documentation](https://pytorch.org/tutorials/beginner/saving_loading_models.html)
[Torch Distributed Elastic — PyTorch 2.1 documentation](https://pytorch.org/docs/stable/distributed.elastic.html)
[TorchElastic Kubernetes — PyTorch 2.1 documentation](https://pytorch.org/docs/stable/elastic/kubernetes.html)
[elastic/kubernetes at master · pytorch/elastic (github.com)](https://github.com/pytorch/elastic/tree/master/kubernetes)
[Kubeflow Pipelines — PyTorch/TorchX main documentation](https://pytorch.org/torchx/latest/pipelines/kfp.html)
[TorchX — PyTorch/TorchX main documentation](https://pytorch.org/torchx/latest/)
[Kubeflow Pipelines — PyTorch/TorchX main documentation](https://pytorch.org/torchx/latest/pipelines/kfp.html)

监控 Pytorch

[Metrics — PyTorch 2.1 documentation](https://pytorch.org/docs/stable/elastic/metrics.html)

Trainer

[Distributed training with 🤗 Accelerate (huggingface.co)](https://huggingface.co/docs/transformers/accelerate)

- [PyTorch 分布式训练实现(DP/DDP/torchrun/多机多卡) - 知乎](https://zhuanlan.zhihu.com/p/489011749)
- [Pytorch - 分布式训练极简体验 - 知乎](https://zhuanlan.zhihu.com/p/477073906)
- [PyTorch分布式训练基础--DDP使用 - 知乎](https://zhuanlan.zhihu.com/p/358974461)
- [开源一个 PyTorch 分布式（DDP）训练 mnist 的例子代码 - 知乎](https://zhuanlan.zhihu.com/p/463842164)
- [Distributed Data Parallel — PyTorch 2.1 documentation](https://pytorch.org/docs/stable/notes/ddp.html)
- [Machine Learning as a Flow: Kubeflow vs. Metaflow | by Roman Kazinnik | Medium](https://roman-kazinnik.medium.com/machine-learning-as-a-flow-kubeflow-vs-metaflow-75f65bd251ec)
- [Ring Allreduce - 简书](https://www.jianshu.com/p/8c0e7edbefb9)
- [GPU高效通信算法——Ring Allreduce](https://picture.iczhiku.com/weixin/message1570798743118.html)
- [Reduced ring - Wikipedia](https://en.wikipedia.org/wiki/Reduced_ring)
- [Machine Learning Distributed: Ring-Reduce vs. All-Reduce | by Roman Kazinnik | Medium](https://roman-kazinnik.medium.com/machine-learning-distributed-ring-reduce-vs-all-reduce-cb8e97ade42e)
- [【转载】 Ring Allreduce (深度神经网络的分布式计算范式 -------------- 环形全局规约) - Angry_Panda - 博客园](https://www.cnblogs.com/devilmaycry812839668/p/12446933.html)
- [ddp 多卡训练torch 记录_torch ddp 卡死-CSDN博客](https://blog.csdn.net/weixin_43850253/article/details/131706419)
- [pytorch多卡分布式训练简要分析 - 知乎](https://zhuanlan.zhihu.com/p/159404316)
- [Distributed data parallel training in Pytorch](https://yangkky.github.io/2019/07/08/distributed-pytorch-tutorial.html)
- [Pytorch中的Distributed Data Parallel与混合精度训练（Apex） - 知乎](https://zhuanlan.zhihu.com/p/105755472)
- [Pytorch 分散式訓練 DistributedDataParallel — 實作篇 | by 李謦伊 | 謦伊的閱讀筆記 | Medium](https://medium.com/ching-i/pytorch-%E5%88%86%E6%95%A3%E5%BC%8F%E8%A8%93%E7%B7%B4-distributeddataparallel-%E5%AF%A6%E4%BD%9C%E7%AF%87-35c762cb7e08)
- [Multi-GPU training — PyTorch Lightning 1.4.9 documentation](https://pytorch-lightning.readthedocs.io/en/1.4.9/advanced/multi_gpu.html)
- [Deepspeed 大模型分布式框架精讲 - 哔哩哔哩 bilibili](https://www.bilibili.com/video/BV1mc411y7jW/?spm_id_from=333.1007.tianma.10-4-38.click&vd_source=f0545eb2f2f0269a5a9941436ba53b7d)

## 参考资料

[^1]: [Getting Started with Distributed Data Parallel — PyTorch Tutorials 2.2.0+cu121 documentation](https://pytorch.org/tutorials/intermediate/ddp_tutorial.html)
