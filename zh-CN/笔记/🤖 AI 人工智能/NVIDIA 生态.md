---
tags:
  - AI/硬件/GPU/NVIDIA
  - AI/硬件/GPU/NVIDIA/GPU
  - AI
  - 索引
  - 索引/资源列表
  - 分类/收集箱
---
# NVIDIA 生态

- CUDA
- CUBLAS runtime
- The CUDA runtime library
- CUFFT runtime
- CUrand runtime
- CUsparse rutime
- CUsolver runtime
- NPP runtime
- nvblas runtime
- NVTX runtime
- NVgraph runtime
- NVjpeg runtime
- NVRTC/NVVM runtime
- [cuDNN (CUDA Deep Neural Network)](https://developer.nvidia.com/cudnn)
- nvcc
- [cuda-toolkit](https://anaconda.org/nvidia/cuda-toolkit)
- [cudnn-frontend](https://github.com/NVIDIA/cudnn-frontend)
- [anaconda](https://anaconda.org/)
- [Megatron](https://github.com/NVIDIA/Megatron-LM)
[NeMo](https://docs.nvidia.com/deeplearning/nemo/user-guide/docs/en/main/nlp/megatron.html)
- [TransformerEngine](https://docs.nvidia.com/deeplearning/transformer-engine) [Repo](https://github.com/NVIDIA/TransformerEngine)
- [NVIDIA/NVTX](https://github.com/NVIDIA/NVTX) The NVIDIA® Tools Extension SDK (NVTX) is a C-based Application Programming Interface (API) for annotating events, code ranges, and resources in your applications.
- [Triton](https://github.com/triton-inference-server/server)
- nvml
- nccl
- dcgm
- [Nsight](https://developer.nvidia.com/nsight-systems)

## ngc
Data Science, Machine Learning, AI, HPC Containers | NVIDIA NGC
https://catalog.ngc.nvidia.com/containers

## 参考资料

python - Is it still necessary to install CUDA before using the conda tensorflow-gpu package? - Stack Overflow
https://stackoverflow.com/questions/61533291/is-it-still-necessary-to-install-cuda-before-using-the-conda-tensorflow-gpu-pack
