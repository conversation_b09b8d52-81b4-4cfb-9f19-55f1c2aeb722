---
tags:
  - AI
  - AI/大语言模型
  - AI/大语言模型/LLM
  - AI/提示词/Prompt
  - AI/提示词
---
# 最佳实践

「提示词」在某种程度上来说，就是提问的艺术，就是沟通的艺术。

GPT 和 LLM 会有：

- 幻觉
- 阿谀奉承
- 被提问带偏（比如 XY 问题）

这些问题都可以解决，它要求：

- 如果模型较小，那么请把要解决的问题所需要的资料全部都告诉 GPT/LLM，这样 GPT/LLM 可以在受控的上下文中解决问题，而非自行脑补问题。这也被称之为 within context 或者 pattern following。
- GPT/LLM 擅长解决一切类似于语言的问题，越是结构化清晰的，概率计算越精确。
	- 代码补全
- GPT/LLM 擅长下面的几种任务
	- 非结构化信息到非结构化信息的转化
		- 能力：
			- 聊天
			- 翻译
			- 比对
			- 改写，或者说，润色
			- 转写，或者说，洗稿，去查重率
			- 扩写
			- 续写
			- 仿写
  - 非结构化信息到结构化信息的转化
    - 影响的领域：
      - 医院
        - 病史记录
        - 诊断记录
        - 处方
      - 学校
      - 法院
      - 银行
      - 出版社
    - 能力
      - 分类
      - 记录
      - 回复
      - 客服
      - 提取文本中的数据，信息，特征
      - 识别态度
      - 财报信息的分析和提取
      - 总结
      - 信息分类
      - 会议总结
      - 格式排版
      - 进程报告
	- 结构化信息到非结构化信息的转化

1. As much specific and descriptive as possible
2. 有层次有结构的 prompt
3. 给出预期（one-shot，few-shot）

在需要 GPT/LLM 帮你解答问题的时候，请说清楚：

1. 我们是谁
2. 我们现在在哪里
3. 做了什么尝试
4. 希望得到什么结果
