# 开发工具

![[Pasted image 20240816155220.png]]

Promptmetheus · Prompt Engineering IDE
https://promptmetheus.com/

promptslab/Promptify: Prompt Engineering | Prompt Versioning | Use GPT or other prompt based models to get structured output. Join our discord for Prompt-Engineering, LLMs and other latest research
https://github.com/promptslab/Promptify

deepset-ai/haystack: :mag: LLM orchestration framework to build customizable, production-ready LLM applications. Connect components (models, vector DBs, file converters) to pipelines or agents that can interact with your data. With advanced retrieval methods, it's best suited for building RAG, question answering, semantic search or conversational agent chatbots.
https://github.com/deepset-ai/haystack