---
tags:
  - 开发/后端/分布式
  - 知识领域/分布式
  - 分类/收集箱
  - 网站/GiHub
---

# 分布式可用的 ID 们

[Online UUID Generator Tool](https://www.uuidgenerator.net/)

[通用唯一识别码 - 维基百科，自由的百科全书](https://zh.wikipedia.org/zh-cn/%E9%80%9A%E7%94%A8%E5%94%AF%E4%B8%80%E8%AF%86%E5%88%AB%E7%A0%81)

[bwmarrin/snowflake: A simple to use Go (golang) package to generate or parse Twitter snowflake IDs](https://github.com/bwmarrin/snowflake)

[paralleldrive/cuid: Collision-resistant ids optimized for horizontal scaling and performance.](https://github.com/paralleldrive/cuid)

[paralleldrive/cuid2: Next generation guids. Secure, collision-resistant ids optimized for horizontal scaling and performance.](https://github.com/paralleldrive/cuid2)

[segmentio/ksuid: K-Sortable Globally Unique IDs](https://github.com/segmentio/ksuid)

[ai/nanoid: A tiny (109 bytes), secure, URL-friendly, unique string ID generator for JavaScript](https://github.com/ai/nanoid)

GUID
