# 调色板设计入门 101

为什么需要为 UI 定制调色板？

大多数现有的应用程序基于几何概念（如互补色或三角排列）生成配色方案。虽然这些方案可能适用于徽标或传统平面设计，但它们往往不适合UI设计。在UI设计中，你通常需要使用一些基本颜色（通常由品牌指南规定）以及各种阴影和变化。

本教程会教你如何配置出一套你自己的调色板

## 理论知识

### 认识 HSL 颜色

HSL 是一种表示颜色的方法，它使用人眼直观感知的三个属性：色相、饱和度和亮度。

- **H：Hue**，色相，0-360°，表示颜色的种类

其中0°为红色，120°为绿色，240°为蓝色。

![[Pasted image 20240618222515.png]]

- **S：Saturation**，饱和度，0-100%，表示颜色的纯度

是颜色看起来多么鲜艳或生动。0% 的饱和度是灰色（无颜色），100% 的饱和度是鲜艳且强烈的。

![[Pasted image 20240618222631.png]]

没有饱和度时，色调是无效的——当饱和度为0%时旋转色调实际上不会改变颜色。

![[Pasted image 20240618222639.png]]

- **L：Lightness**，亮度，0-100%，表示颜色的明暗

它衡量颜色接近黑色或白色的程度。0% 亮度是纯黑色，100% 亮度是纯白色，而50% 亮度是在给定色调下的纯颜色。

![[Pasted image 20240619151152.png]]

### 网站需要很多颜色

你是否曾使用过调色板生成器，选择一个起始颜色，调整选项，然后得到五个推荐的颜色？

![[Pasted image 20240618223748.png]]

除非想让网站看起来像这样，否则它并不是很有用：

![[Pasted image 20240618223807.png]]

所以不能用五个十六进制代码完成所有的事情。要构建网站，需要一个更全面的颜色集。

![[Pasted image 20240618224022.png]]

## 相关工具

### 🎨 Palettte App —— 调色板编辑器：
https://palettte.app/

### 🍱 UI Colors —— 完善的一站式调色板生成器：
https://uicolors.app/create

## 设计调色板

这里我们需要用到上面提到的两个工具，Palettte App 和 UI Colors。

![[Pasted image 20240618231140.png]]

#### 先选择基色

打开 Palettte App，选择一个基色，这个基色通常是品牌指南中定义的颜色。

如果你的 Palettte App 中有好几种颜色，你可以先删掉其它的，只留下一个。

![[Pasted image 20240618231500.png]]

在 **Toggle Handles** 菜单中勾选 H、S、V 三个选项，这样可以看到调节颜色的手柄

![[Pasted image 20240618231748.png]]

接下来调整这个颜色。您要为创建的色阶选择一个基色（中间的颜色），您的浅色和深色色调都要以它为基础。

这并没有真正科学的方法，但对于原色和重点色来说，一个好的经验法则是挑选一种**适合作为按钮背景的色调**。

![[Pasted image 20240618232241.png]]

如果你会使用 Figma 或 CSS，你可以把这个颜色应用到一个自己创建的按钮上试试效果，根据效果反复调整到满意为止。

需要注意的是，这里并没有 "从 50% 明度开始 "之类的真正规则--每种颜色的表现都有些不同，所以你必须依靠自己的眼睛来判断。

> [!TIP]
> 不要忘了在暗色模式和亮色模式中都试试你的颜色，或者为暗色模式和亮色模式分别创建调色板。
> 因为当背景色变化时，按钮看起来的感觉也会发生变化。

#### 寻找最浅和最深的颜色

接下来，选择你的最深色调和最浅色调。颜色的最深色通常保留用于文本，而最浅色可能用于给元素的背景着色。

一个简单的 Alert 组件就是一个很好地结合了这两种用例的例子，因此它可以是挑选这些颜色的绝佳地方。

![[Pasted image 20240618232100.png]]

在 Palettte App 中选择中间色之后点两次 **Add Swatch** 按钮，然后拖动 H｜V｜S 三个滑块调整。从与基本颜色色调相匹配的颜色开始，并调整饱和度和亮度，直到您满意为止。

![[Pasted image 20240618232602.png]]
![[Pasted image 20240618235052.png]]

你同样可以在 Figma 或者 HTML + CSS 中创建实际的样式来测试这个颜色。

> [!TIP]
> 如果你在暗色模式中使用这两个颜色构建 Alert 组件，你可能会发现它们看起来不太好。这是因为在暗色模式中，文本颜色应该更亮，而背景颜色应该更暗。
> 所以你需要为暗色模式重新选择这两个颜色，为暗色模式和亮色模式分别创建调色板。

#### 填补空白

有了底色、深色和浅色之后，就需要填补它们之间的空白。

对于大多数项目来说，每种颜色至少需要 5 个色调，如果不想太拘束，可能需要 10 个色调。

9 是一个很好的数字，因为它很容易分割，而且填色也更简单。我们把最深的色调称为 900，基本色调称为 500，最浅的色调称为 100。

![[Pasted image 20240618232924.png]]

首先选择色调 700 和 300，也就是位于空白处中间的色调。你希望这两种色调是两边色调的完美折衷。

![[Pasted image 20240618232940.png]]

这样，色阶中就多了四个空隙（800、600、400 和 200），你可以用同样的方法来填补它们。

![[Pasted image 20240618233038.png]]

您最终应该获得一套相当平衡的颜色，这些颜色提供足够的选项来适应您的设计理念，而不会感到局限。

完成后点击 “Export Palettes” 导出结果。你可以通过 CSS 变量，Tailwind CSS、UnoCSS 或者 Figma 把它们应用到你的项目中。

![[Pasted image 20240619153923.png]]

## 总结

1. 选择一个基色 —— 适合作为按钮背景的色调
2. 寻找最浅和最深的颜色 —— 用于文本和背景
3. 填补空白 —— 至少 5 个色调，最多 10 个色调

调色板不是一门科学，尽管很诱人，但你不能纯粹依靠数学来制作完美的调色板。

像上面描述的系统化方法可以帮助你入门，但不要害怕在需要时做一些小调整。

一旦你开始在设计中使用你的颜色，你几乎不可避免地会想要调整某个色调的饱和度，或者让几个色调更亮或更暗。相信你的眼睛，而不是数字。

## 参考资料｜拓展阅读

- [Refactoring UI - Building Your Color Palette](https://refactoringui.com/previews/building-your-color-palette/)
- [Palettte App: The advanced color palette editor](https://gabrielschneider.de/palettte-app/)
- [Color in UI Design: A (Practical) Framework](https://www.learnui.design/blog/color-in-ui-design-a-practical-framework.html)
- [Mastering Multi-hued Color Scales with Chroma.js](https://www.vis4.net/blog/mastering-multi-hued-color-scales/)

- [Palettte App](https://palettte.app/)
- [UI Colors](https://uicolors.app/create)
