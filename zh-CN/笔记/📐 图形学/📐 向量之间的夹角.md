---
tags:
  - 数学
  - 知识领域/数学
  - 数学/向量
  - 数学/线性代数
  - 计算机/计算机图形学
  - 知识领域/计算机图形学
  - 数学/点乘
---
# 📐 向量之间的夹角

##### 公式

$$
\theta = arcos(\frac{\vec a·\vec b}{\|\vec a\|\|\vec b\|})
$$

##### 对于单位向量

$$
\theta = arcos(\hat a· \hat b)
$$

#### 代码参考

```typescript
export function includedAngle(a: number[], b: number[]) {
  return Math.acos(dotProduct(a, b) / (len(a) * len(b)))
}
```

> dotProduct: 点乘
>
> add: 加法 a + b
>
> len: 求向量矩阵的长度
