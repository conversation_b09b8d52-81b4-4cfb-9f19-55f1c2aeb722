---
tags:
  - 数学
  - 数学/矩阵
  - 计算机/计算机图形学
  - 知识领域/计算机图形学
  - 知识领域/数学
  - 数学/线性代数
  - 数学/高等数学
---
# 🔢 矩阵乘法

假设有 a 和 b 两个矩阵，那么只有 a 的列数和 b 的行数相等时才可以进行矩阵乘法。得到的结果矩阵的大小是 a 的行数和 b 的列数：

(M x N)(N x P) = (M x P)

注：*(行数 x 列数)*

#### 每个单项的计算方法

![](assets/image-20211001132033869.png)

计算 x 行 y 列 位置的结果时，只需将 **a 矩阵中 x 行** 和 **b 矩阵中 y 列**的向量进行**点乘**即可。
$$
\begin{equation}\begin{split}
(5, 2) \cdot (9, 8)= 61 \\
= 5*9+2*8

\end{split}\end{equation}
$$

#### 矩阵乘法**没有交换律**，但是**符合结合律**

 ![矩阵乘法交换与结合](assets/image-20230702160104072.png)
