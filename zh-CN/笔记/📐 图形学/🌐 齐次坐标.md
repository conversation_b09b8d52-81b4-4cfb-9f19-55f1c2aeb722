---
tags:
  - 数学
  - 数学/高等数学
  - 知识领域/数学
  - 计算机/计算机图形学
  - 知识领域/计算机图形学
  - 数学/向量
  - 数学/矩阵
---
# 🌐 齐次坐标

## 齐次坐标系的性质

- 投影平面上的任何点都可以表示成一三元组(X,Y,Z)，称之为该点的’齐次坐标或投影坐标，其中 X、Y 及 Z 不全为 0。

- 以齐次坐标表表示的点，若该坐标内的数值全乘上一相同非零实数，仍会表示该点。
- 相反地，两个齐次坐标表示同一点，当且仅当其中一个齐次坐标可由另一个齐次坐标乘上一相同非零常数得取得。
- 当 Z 不为 0，则该点表示欧氏平面上的该(X/Z,Y/Z)。
- 当 Z 为 0，则该点表示一无穷远点。 注意，三元组(0,0, 0)不表示任何点。原点表示为(0, 0, 1)。为与以与笛卡儿坐标相区别，如以冒号代替逗号，以 (x:y:z) 代替(x,y,z)，以强调该坐标有着比例的性质。亦有以方括号代替括弧，以[x,y,z]来强调有多个坐标表示同一个点。有些作者则会同时使用冒号与方括号，如 [x:y:z]。

## 齐次坐标系的重要性

#### 1. 区分向量和点

​		一个三维坐标的三个分量x，y，z用齐次坐标表示为变为x，y，z，w的四维空间，变换成三维坐标是方式是x/w,y/w,z/w，当w为0时，在数学上代表无穷远点，即并非一个具体的坐标位置，而是一个具有大小和方向的向量。从而，通过w我们就可以用同一系统表示两种不同的量。
  在OPENGL中，作为坐标点时，w参数为1，否则为0，如此一来，所有的几何变换和向量运算都可以用相同的矩阵乘积进行运算和变换，当一个向量和一个矩阵相乘时所得的结果也是向量。

#### 2. 易于进行仿射变化(Affine Transformation)

​		仿射变换，又称仿射映射，是指在几何中，一个向量空间进行一次线性变换并接上一个平移，变换为另一个向量空间。引入齐次坐标系，可以将一个仿射变换对应于一个矩阵和一个向量的乘法。
