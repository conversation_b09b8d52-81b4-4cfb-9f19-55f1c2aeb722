---
tags:
  - 运维/云原生/Kubernetes
  - 运维/云原生/Kubernetes/K8s
  - 开发/云原生/Kubernetes/K8s
  - 开发/云原生/Kubernetes
  - 运维/云原生/Kubernetes
  - 命令行
  - 命令行/k9s
  - 开源/软件/k9s
  - 运维/云原生/Kubernetes/工具/k9s
status: 尚未完成
---
# k9s 速查表

## 导航

### `d` 描述 Describe

<kbd>D</kbd>
### `esc` 退出视图

<kbd>esc</kbd>
### `:namespace` 进入命名空间选择器

速记版本：

```shell
:namespace
```

按键顺序可视化：

<kbd data-keyboard-key="shift">Shift</kbd> + <kbd>:</kbd> + <kbd>namespace</kbd> + <kbd>Enter</kbd>

### `:ctx` 选择集群配置（Context）

速记版本：

```
:ctx
```

按键顺序可视化：

<kbd data-keyboard-key="shift">Shift</kbd> + <kbd>:</kbd> + <kbd>ctx</kbd> + <kbd>Enter</kbd>

## 数据处理

### `x` 解码 Secret（秘密）

<kbd>X</kbd>
## 参考资料

[k9s Cheatsheet](https://www.hackingnote.com/en/cheatsheets/k9s/)
