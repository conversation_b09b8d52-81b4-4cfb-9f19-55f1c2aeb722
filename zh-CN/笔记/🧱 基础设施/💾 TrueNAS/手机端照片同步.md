# 手机端照片同步

现在能够自动备份的 App 只有：

1. 群晖的 Moments
2. 微软的 OneDrive
3. 谷歌的 Google Photos

有人提到：

> 先说我尝试过的所有 app ，包括各类网盘，比如阿里，天翼等，都无法做到无感自动同步，都需要打开 app 才能备份照片，那些什么后台备份的开关打开根本没卵用。目前我测试过（也没有专门大规模测试）真的能够后台全自动无感同步的 app 就俩，google photos 和群晖的 photos 。
>
> 好了，回答楼主问题。鉴于 syncthing 在 ios 付费，所以我也没考虑用，现在用的方案是先不定期打开天翼网盘同步到天翼网盘，然后 server 上装了个 [https://github.com/tickstep/cloudpan189-go](https://github.com/tickstep/cloudpan189-go),定期从天翼下载到 server 上，你的问题我算回答完了。然后，我有一台 pixel3 ，可以无限容量，故 server 和 pixel 都安装 syncthing 进行同步，然后我的照片就又都到无限容量的 google photos 上了~
>
> 另外因为黑裙我刚组好，还在折腾还没折腾完，折腾好了立马弃用天翼用群晖 photos 替代，当时测试群晖 photos 可以无感备份的时候不知道多开心，哈哈~~

| 方案名称 | 网站 | 方案类型和介绍 | 优点 | 缺点 | 设备支持 | 语言 |
| --------- | --------------- | ---- | ---- | ---- | -------- | -------- |
| ownCloud | [链接](https://owncloud.com/) | 私有云盘服务 | 1. 老牌的私有云盘服务，资料充足，Bug 会少一些 | 1. 设计上有很多痛点，可以选择同一个团队制作的 NextCloud | 1. iPhone <br>2. iPad | 简体中文 |
| NextCloud | [链接](https://nextcloud.com/) | 私有云盘服务 | 1. 是 OwnCloud 的核心团队成员分离出来单独开发的开源方案，解决了很多 OwnCloud 的问题和痛点<br>2. 能外挂 WebDAV（网页 HTTP 协议的网络硬盘协议）硬盘、Google 网盘、FTP 等等<br>3. NextCloud 的文件是完整存储在服务器上，如果网盘挂了，直接把存储文件复制出来就可以了 | 1. 性能不好<br>2. 预览效果不如 Seafile[^2]<br>3. iOS 端的 App 中的**自动上传功能**形同虚设，设置时只能针对整个[^1]手机相册新增的图片/视频，对应到服务端也是只有一个路径，也就是说没办法像 Android 端那样分相册/文件夹的灵活配置自动上传。最终我只能每隔一段时间手动去上传每一个相册的新照片/视频。 | 1. iPhone<br>2. iPad | 简体中文 |
| Syncthing | [链接](https://syncthing.net/) | 本地同步服务 | 1. 服务简单，架构简单<br>2. 自称是持续同步 | ？ | ？ | ？ |
| PhotoSync |  | 软件直接对接服务 | 1. 可以接 SMB，FTP 等协议，无需服务器<br>2. 软件简单易用 | 1. 似乎只支持照片视频等内容 | 1. iPhone<br>2. iPad | 简体中文 |
| Seafile | [链接](https://www.seafile.com/home/<USER>
| Resilio Sync | [链接](https://www.resilio.com/individuals/) | 老牌的 P2P 同步和共享工具 | 1. P2P，只要有节点 Peer 就能够连接上然后进行传输<br>2. 速度取决于 P2P 节点的效率，本质上和 BitTorrent 的 BT 种子协议没有太大区别 |  | 1. iPhone<br>2. iPad | 繁体中文 |

[^1]: [不懂就问： 如何把 ios 设备的照片自动备份到局域网 nas！ - V2EX](https://www.v2ex.com/t/829873)
[^2]: [Seafile和Nextcloud相比较哪个好用_dayou7738的博客-程序员宝宝 - 程序员宝宝](https://cxybb.com/article/dayou7738/102318544)
[^3]: [Seafile和Nextcloud相比较哪个好用_dayou7738的博客-程序员宝宝 - 程序员宝宝](https://cxybb.com/article/dayou7738/102318544)
[^4]: [Seafile和Nextcloud相比较哪个好用_dayou7738的博客-程序员宝宝 - 程序员宝宝](https://cxybb.com/article/dayou7738/102318544)
[^5]: [Seafile和Nextcloud相比较哪个好用_dayou7738的博客-程序员宝宝 - 程序员宝宝](https://cxybb.com/article/dayou7738/102318544)
