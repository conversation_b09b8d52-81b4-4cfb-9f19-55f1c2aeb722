---
tags:
  - 命令行/终端
  - 操作系统/Unix
  - 操作系统/Linux
---

# Linux 命令索引

## 文件管理

| 文档状态 | 命令 | 简介 |
| --------- | ----| ----- |
| 正常 | [[cd 变更目录]] | 变更当前 **工作目录** 的位置 |
| 正常 | [[cp 复制]] | 把文件复制到别的地方 |
| 正常 | [[file 获取文件信息]] | 获取文件的信息、编码、格式、大小等等信息 |
| 正常 | [[find 查找]] | 查找文件和目录 |
| 正常 | [[ls 列出文件]] | 看到当前 **工作目录** 下的目录和文件 |
| 正常 | [[mkdir 创建目录]] | 创建目录 |
| 正常 | [[mv 剪贴、移动]] | 把文件移动到别的地方（重命名） |
| 正常 | [[rm 移除文件]] | 删除文件或者目录 |
| 正常 | [[touch 创建文件]] | 修改文件或者目录的时间属性，若文件不存在，系统会建立一个新的文件 |
| 正常 | [[tree 树状图列出文件]] | 以树状图列出目录的内容 |
| 正常 | [[which where 获取命令对应的可执行文件]] | 获取可执行命令的具体位置 |

## 数据处理

| 文档状态 | 命令 | 简介 |
| --------- | ----| ----- |
| 正常 | [[base64]] | 编解码 base64 |
## 文档读写

| 文档状态 | 命令 | 简介 |
| --------- | ----| ----- |
| 正常 | [[cat 输出文件]] | 把任何文件的内容以文本形式输出到命令行上 |

## 权限管理

| 文档状态 | 命令 | 简介 |
| --------- | ----| ----- |
| 正常 | [[chown 变更所属权]] | 设置文件所有者和文件关联组 |
| 正常 | [[chmod 变更权限]] | 控制用户对文件的权限 |

## 网络通讯

| 文档状态 | 命令 | 简介 |
| --------- | ----| ----- |

## 系统设置

| 文档状态 | 命令 | 简介 |
| --------- | ----| ----- |

## 系统管理

| 文档状态 | 命令 | 简介 |
| --------- | ----| ----- |
| 正常 | [[ps 进程状态]] | 显示当前进程的状态，类似于 windows 的任务管理器中「程序」列表 |

## 磁盘管理

| 文档状态 | 命令 | 简介 |
| --------- | ----| ----- |
