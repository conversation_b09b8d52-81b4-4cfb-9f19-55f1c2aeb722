# Linux 权限

## 说明

-使用 `ls -l` 命令的时候经常会看到这样的结果（`ls` 命令参考 [ls 列出文件](./Linux%20%E5%91%BD%E4%BB%A4/%E6%96%87%E4%BB%B6%E7%AE%A1%E7%90%86/ls%20%E5%88%97%E5%87%BA%E6%96%87%E4%BB%B6.md)）：

```shell
权限        有多少文件链接到该目录 用户  用户组 大小  最后修改时间  标题
drwxrw-rw- 26                  neko:whell 2784 00:00       test
```

## 权限位（bit）表示

这个列表中包含了权限的信息，一般权限的结构如下：

![](assets/image_20211012135645.png)

### 含义

`d` 表示目录，`rwx` 分别是读取（值为 1），写入（值为 2），执行（值为 3）
`d` 后面跟着 9 个字符，权限中，这 9 个字符三个为一组，第一组指定的是 **用户权限** ，第二组指定的是 **组权限** ，第三组指定的是 **其他（所有人）的权限** 。
比如在此处，

1. `d` 说明该是一个目录，目录名称为 test
2. 第一组 `rwx` 说明只要是 `neko` 用户，可以在该目录下进行读取、写入、执行操作
3. 第二组 `rw-` 说明只要是 `wheel` 组的人，可以在该目录下进行读取、写入操作
4. 第三组 `rw-` 说明对于并非是用户 `neko` 或是 `wheel` 用户组的人，都可以进行读取、写入的操作

## 总结

综上所述：

1. 第 **1、4、7** 位表示读权限，如果用 r 字符表示，则有读权限，如果用 - 字符表示，则没有读权限；
2. 第 **2、5、8** 位表示写权限，如果用 w 字符表示，则有写权限，如果用 - 字符表示没有写权限；
3. 第 **3、6、9** 位表示可执行权限，如果用 x 字符表，则有执行权限，如果用 - 字符表示，则没有执行权限

## 权限八进制表示

想要计算为常见的 `700`、`644` 格式也十分简单：
权限的数字形式有三位，第一位对应第一组，第二位对应第二组，以此类推...

![](assets/image_20211012140400.png)

## 含义

每个权限类型对应的数字值为：

| r    | w    | x    | -    |
| ---- | ---- | ---- | ---- |
| 4    | 2    | 1    | 0    |

我们只需要把权限类型加起来就可以得到最终结果。

## 示例

比如 `drwxrw-rw-`，省略第一个 `d`，剩下的权限三个字符为一组，分成三组：

1. 第一组 `rwx`，分别是 4，2，1，相加之后得到 7，第一位为 7
2. 第二组 `rw-`，分别是 4，2，0，相加之后得到 6，第二位为 6
3. 第三组 `rw-`，分别是 4，2，0，相加之后得到 6，第三位为 6

于是 `rwxrw-rw-` 也可以表示为 766。

### 速查表

| 八进制 | 对应的权限 | 位表示 | 二进制 |
| ------- | -----------| ------ | ------- |
| 7 | 读 + 写 + 执行 | rwx | 111 |
| 6 | 读 + 写 | rw- | 110 |
| 5 | 读 + 执行 | r-x | 101 |
| 4 | 只读 | r-- | 100 |
| 3 | 写 + 执行 | -wx | 011 |
| 2 | 只写 | -w- | 010 |
| 1 | 只执行 | --x | 001 |
| 0 | 无 | --- | 000 |
