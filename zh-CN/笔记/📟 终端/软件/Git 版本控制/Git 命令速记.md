---
tags:
  - 命令行/git
---

# Git 命令速记

Git是一个开源的分布式版本控制系统，用于敏捷高效地处理任何或小或大的项目。

建议通过大纲索引快速找到需要使用的命令

## 基础

#### 初始化 (init)

```shell
git init <目录（可选）>
```

在指定目录下创建空的Git仓库。运行时没有参数，以初始化当前目录为git仓库。

#### 克隆 (clone)

```shell
git clone <代码仓库地址>
```

克隆位于 `<代码仓库地址>` 的仓库到本地机器上。原始（origin）可以位于本地文件系统上，也可以通过 HTTP 或 SSH 位于远程机器上。

#### 修改配置 (config)

##### 设置姓名

```shell
git config --local user.name <用户名>
```

定义作者姓名，将会应用到当前版本库中的所有提交。对于只有一个账户的开发者而言，可以使用 --global 标志来给所有 Git 代码库设置当前用户的配置选项。

##### 设置邮箱地址

```shell
git config user.email `<电子邮件>`
```

定义作者电子邮件，将会应用到当前版本库中的所有提交。对于只有一个账户的开发者而言，可以使用 --global 标志来给所有 Git 代码库设置当前用户的配置选项。

#### 暂存更改 (add)

```shell
git add <目录/文件>
```

暂存 `<目录>` 中的所有修改，以便于提交。用 `<文件>` 替换 `<目录>` 来暂存一个特定的文件。

#### 提交更改 (commit)

```shell
git commit -m <提交信息>
```

提交暂存的文件。使用 `<提交信息>` 作为提交消息，不通过文本编辑器来编辑。

#### 文件修改状态 (status)

```shell
git status
```

列出哪些文件是暂存的、未暂存的和未追踪的。

#### 提交历史 (log)

```shell
git log
```

使用默认格式显示整个提交历史。关于自定义参数，请看 **附加选项** 。

#### 查看未暂存的更改 (diff)

```shell
git diff
```

显示索引和工作目录之间的未暂存变更。

## 撤销变更

| 命令                    | 说明                                                         |
| ----------------------- | ------------------------------------------------------------ |
| `git revert <提交哈希>` | 创建新的提交，撤销 `<提交哈希>` 中的所有修改，然后将其应用到当前分支。 |
| `git reset <文件>`      | 从暂存区域移除 `<文件>`，但保留工作目录不变。这可以在不覆盖任何修改的情况下解除文件的缓存。 |
| `git clean -n`          | 显示哪些文件会被从工作目录中删除。使用 -f 标志代替 -n 标志来执行清理。 |

## 覆盖提交历史

| 命令                  | 说明                                                         |
| --------------------- | ------------------------------------------------------------ |
| `git commit ---amend` | 将最后一次提交的内容和最后一次提交的内容结合起来替换。在没有任何分期的情况下使用，可以编辑最后一次提交的信息 |
| `git rebase <基准>`   | 将当前分支重新归入 `<基准>`。`<基准>` 可以是一个提交编号、分支名称、一个标签，或者是对 HEAD 的相对引用。 |
| `git reflog`          | 显示本地版本库HEAD的修改日志。添加 --relative-date 标志来显示日期信息，或者 --all 来显示所有参考。 |

## 分支操作

| 命令                      | 说明                                                         |
| ------------------------- | ------------------------------------------------------------ |
| `git branch`              | 列出你的代码仓库中的所有分支。添加 `<分支名>` 参数，创建一个名称为 `<分支名>` 的新分支。 |
| `git checkou -b <分支名>` | 创建并签出一个名为 `<分支名>` 的新分支。去掉 -b 标志，签出一个现有的分支。 |
| `git merge <分支名>`      | 将 `<分支名>` 合并到当前分支。                                 |

## 远程代码库

| 命令                                       | 说明                                                         |
| ------------------------------------------ | ------------------------------------------------------------ |
| `git remote add <标记名称> <远程地址>`     | 创建一个新的连接到一个远程版本库。添加远程后，你可以在其他命令中使用 `<标记名称>` 作为 `<远程地址>` 的快捷方式。 |
| `git fetch <远程地址/标记名称> <分支名称>` | 从版本库中获取一个特定的 `<分支名称>` 。不使用 `<分支名称>` 来获取所有的远程参考。 |
| `git pull <远程地址/标记名称>`             | 获取当前分支的指定远程副本，并立即将其合并到本地副本。       |
| `git push <远程地址/标记名称> <分支名称>`  | 将该分支与必要的提交和对象一起推送到 `<远程地址/标记名称>` 。如果分支不存在，则在远端 repo 中创建命名的分支。 |

## Git 配置

| 命令                                           | 说明                                                         |
| ---------------------------------------------- | ------------------------------------------------------------ |
| `git config --global user.name <用户名>`       | 定义作者的名字，用于当前用户的所有提交。                     |
| `git config --global user.email <电子邮件>`    | 定义作者的电子邮件，用于当前用户的所有提交。                 |
| `git config --global alias. <别名> <git 命令>` | 创建 Git 命令的快捷方式。例如，alias.glog "log --graph --oneline" 将设置 "git glog "等同于 "git log --graph --oneline"。 |
| `git config --system core.editor <编辑器>`     | 设置机器上所有用户的命令所使用的文本编辑器。`<编辑器>` 参数应该是启动所需编辑器的命令（例如，vi）。 |
| `git config --global --edit`                   | 在文本编辑器中打开全局配置文件进行手动编辑。                 |

## Git 记录

| 命令                             | 说明                                                         |
| -------------------------------- | ------------------------------------------------------------ |
| `git log -<上限>`                | 用 `<上限>` 来限制提交的数量。例如，"git log -5 "将限制为5个提交。 |
| `git log --oneline`              | 将每个提交浓缩为一行。                                       |
| `git log -p`                | 显示每个提交的完整差异。 |
| `git log --stat`                 | 包括哪些文件被修改了，以及每个文件中被添加或删除的相对行数。 |
| `git log --author= "<文本模式>"` | 搜索某个特定作者的提交内容。                                 |
| `git log --grep="<文本模式>"`    | 搜索提交信息符合 `<文本模式>` 的提交。                         |
| `git log <开始>..<结束>`         | 显示发生在 `<开始>` 和 `<结束>` 之间的提交。参数可以是提交ID、分支名称、HEAD或任何其他类型的修订参考。 |
| `git log -- <文件>`              | 只显示有指定文件的提交。                                     |
| `git log --graph --decorate`     | --graph 标志会在提交信息的左侧绘制一个基于文本的提交图表。msgs。--decorate（装饰）在提交信息的左侧添加分支的名称或标签。 |

## Git Diff 对比

| 命令                | 说明                                   |
| ------------------- | -------------------------------------- |
| `git diff HEAD`     | 显示工作目录和最后一次提交之间的差异。 |
| `git diff --cached` | 显示暂存的修改与最后一次提交的差异。   |

## Git Reset 重设

| 命令                      | 说明 |
| ------------------------- | ---- |
| `git reset`               | 重置暂存区域，使之与最近的提交相匹配，但保留工作目录不变。 |
| `git reset --hard` | 重置暂存区域和工作目录以匹配最近的提交，并覆盖工作目录中的所有修改。 |
| `git reset <提交哈希>` | 将当前分支提示向后移动到 `<提交哈希>`，重置暂存区域以匹配，但不影响工作目录。 |
| `git reset --hard <提交哈希>` | 与前述相同，但将暂存区和工作目录都重置为匹配。删除未提交的修改，以及 `<提交哈希>` 之后的所有提交。 |

## Git Rebase 变基

| 命令                   | 说明                                                         |
| ---------------------- | ------------------------------------------------------------ |
| `git rebase -i <基准>` | 使用交互模式将当前分支重新归入 `<基准>`。启动编辑器，输入命令，说明如何将每个提交转移到新基地。 |

## Git Pull 拉取

| 命令                                    | 说明                                                         |
| --------------------------------------- | ------------------------------------------------------------ |
| `git pull --rebase <远程连接/标记名称>` | 获取远程的当前分支的副本，并将其重新归入本地副本。使用 git rebase 相似的模式而不是 merge 模式来整合各分支。 |

## Git Push 推送

| 命令                                   | 说明                                                         |
| -------------------------------------- | ------------------------------------------------------------ |
| `git push <远程连接/标记名称> --force` | 强制执行git推送，即使它的结果是非快进式合并。不要使用 --force 标志，除非你绝对确定你知道自己在做什么。 |
| `git push <remote> --all`              | 推送你所有的本地分支到指定的远程。                           |
| `git push <远程连接/标记名称> --all`   | 当你推送一个分支或使用  --all 标志。--tags 标志会将你所有的本地标签推送到远程 repo 上。 |
