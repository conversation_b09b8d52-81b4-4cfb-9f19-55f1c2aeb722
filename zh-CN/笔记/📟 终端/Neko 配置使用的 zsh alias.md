---
tags:
  - 命令行/终端
  - 开源/软件/zsh
---
# Neko 配置使用的 `zsh` alias

## `zsh`

### 快速重载 `zsh` 和打开 `.zshrc` 配置

```shell
# Zsh ------------------------------------------------------------------------------

alias reload="source ~/.zshrc"
alias zshrc="code ~/.zshrc"

# ----------------------------------------------------------------------------------
```

### 快速打开 `ssh` 配置

```shell
# Zsh ------------------------------------------------------------------------------

alias sshconfig="code ~/.ssh/config"

# ----------------------------------------------------------------------------------
```
## 其他的 alias 配置

[[获取当前 Linux 机器的公网 IP 地址]]