---
tags:
  - 命令行
  - 命令行/brew
  - 操作系统/macOS
---
# 如何统计当前目录下所有源代码的行数

## 前置条件

如果还没有安装，在 macOS 上可以使用

```shell
brew install cloc
```

进行安装

## 执行脚本

```shell
cloc $(bash -c 'find . -print0 | while IFS= read -r -d "" file
do
    echo "$file"
done' | grep -v ".git")
```

### 脚本解释

上述脚本中的

```shell
$(bash -c 'find . -print0 | while IFS= read -r -d "" file
do
    echo "$file"
done' | grep -v ".git")
```

一段将会使用 find 命令递归筛选出当前目录下的所有文件和子目录中的文件，并使用 grep 命令排除 .git 开头的文件。

最后将上述脚本的输出给到 cloc 命令后就能了解当前目录下的代码计数了：



```shell
     315 text files.
     303 unique files.
      27 files ignored.

github.com/AlDanial/cloc v 1.94  T=0.25 s (1222.8 files/s, 306635.2 lines/s)
-------------------------------------------------------------------------------
Language                     files          blank        comment           code
-------------------------------------------------------------------------------
Go                             279          10149           4313          54404
SQL                              6             58            159           4673
YAML                            11            148            306           1240
Bourne Shell                     2             30             36            170
Markdown                         1             57              0            126
HTML                             1              0              0             36
Dockerfile                       1             18             19             23
JSON                             2              0              0             18
-------------------------------------------------------------------------------
SUM:                           303          10460           4833          60690
-------------------------------------------------------------------------------
```
