# `chmod` 变更权限

## 说明

**ch**ange **mod**e，变更模式，使用该命令可以控制用户对文件的权限

Linux/Unix 的文件调用权限分为三级 : 文件所有者（Owner）、用户组（Group）、其它用户（Other Users）。具体参考：[Linux 权限](../../Linux%20%E6%9D%83%E9%99%90.md)
![](../../assets/image_20211012135645.png)
只有文件所有者和超级用户可以修改文件或目录的权限。可以使用绝对模式（八进制数字模式），符号模式指定文件的权限。
![](../../assets/image_20211012140400.png)

### 语法

此处 `-cfvR` 是参数，可选项

```shell
chmod [-cfvR] [--help] [--version] <模式> <路径>...
```

#### 模式的语法

```shell
[ugoa...][[+-=][rwxXst]...][,...]
```

##### 变更时加上 `u`/`g`/`o`/`a` 的含义

1. `u` 表示该文件的拥有者，加上了之后只会编辑用户（第一组）的值
2. `g` 表示与该文件的拥有者属于同一个用户组，加上了之后只会编辑用户组（第二组）的值
3. `o` 表示其他以外的人，加上了之后只会编辑其他用户（第三组）的值
4. `a` 表示这三者皆是，加上了之后全部都会被影响

##### 变更时加上 +-= 的含义

1. `+` 表示增加权限
2. `-` 表示取消权限
3. `=` 表示唯一设定权限

##### 变更时加上 rwxX 的含义

1. `r` 表示可读取
2. `w` 表示可写入
3. `x` 表示可执行
4. `X` 只有当文件为目录文件，或者其他类型的用户有可执行权限时，才将文件权限设置可执行
5. `s` 表示当文件被执行时，根据 who 参数指定的用户类型设置文件的 setuid 或者 setgid 权限
6. `t` 表示设置粘贴位，只有超级用户可以设置该位，只有文件所有者u可以使用该位

### 示例

1. 将文件 file1.txt 设为所有人皆可读取 :

```shell
chmod ugo+r file1.txt
```

2. 将文件 file1.txt 设为所有人皆可读取 :

```shell
chmod a+r file1.txt
```

3. 将文件 file1.txt 与 file2.txt 设为该文件拥有者，与其所属同一个群体者可写入，但其他以外的人则不可写入 :

```shell
chmod ug+w,o-w file1.txt file2.txt
```

4. 为 ex1.py 文件拥有者增加可执行权限:

```shell
chmod u+x ex1.py
```

5. 将目前目录下的所有文件与子目录皆设为任何人可读取 :

```shell
chmod -R a+r *
```

6. 此外chmod也可以用数字来表示权限如 :

```shell
chmod 777 file
```

语法：

```shell
chmod abc file
```

其中a,b,c各为一个数字，分别表示User、Group、及Other的权限。
每个权限类型对应的数字值为：

| r    | w    | x    | -    |
| ---- | ---- | ---- | ---- |
| 4    | 2    | 1    | 0    |

- 若要 rwx 属性则 4+2+1=7；
- 若要 rw- 属性则 4+2=6；
- 若要 r-x 属性则 4+1=5。

```shell
chmod a=rwx file
```

和

```shell
chmod 777 file
```

效果相同

```shell
chmod ug=rwx,o=x file
```

和

```shell
chmod 771 file
```

## 参数

### 其他参数

| 参数 | 说明 |
| -----| -----|
| **-c** | 若该文件权限确实已经更改，才显示其更改动作 |
| **-f** | 若该文件权限无法被更改也不要显示错误讯息 |
| **-v** | 显示权限变更的详细资料 |
| **-R** | 对目前目录下的所有文件与子目录进行相同的权限变更(即以递归的方式逐个变更) |
| **--help** | 显示辅助说明 |
| **--version** | 显示版本 |
