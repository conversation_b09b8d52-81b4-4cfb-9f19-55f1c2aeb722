# `ls` 列出文件

## 说明

**l**i**s**t，列表的含义，使用这个命令可以看到当前 **工作目录** 下的目录和文件

### 语法

```shell
ls [-alrtAFR] [路径...]
```

### 示例

```shell
$ ls
Git  source
```

## 参数

### 展示隐藏文件 - 参数 a

通常目录中还会存在一些隐藏文件，比如以英文句点 `.` 开头的文件，这些文件默认是不会被 `ls` 列举出来的，需要添加 **参数 `a`**，`a` 指的是 all（全部）

示例：

```shell
$ ls -a
.   .bash_logout  .viminfo  .yarn  .z .zshrc.pre-oh-my-zsh
```

### 列表形式打印 - 参数 l

横着打印的话并不是很方便去阅读和理解，我们可以把 `ls` 命令的结果使用列表的形式展示出来，需要添加**参数 `l`**，`l` 指的是 list（列表）

```shell
$ ls -l
总用量 36
drwxrwxr-x 4 <USER> <GROUP>    28 7月  30 14:33 Git
drwxrwxr-x 3 <USER> <GROUP>    23 9月  29 16:17 source
```

列表中有许多信息，这些信息可以通过下面的列表来一一对应查看：

| 权限 | 链接数 | 归属用户 | 归属用户组 | 大小 | 最后更新月份 | 最后更新日期 | 最后更新时间 | 名称 |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | -- |
| drwxrwxr-x | 4 | neko | neko | 28 | 7月 | 30 | 14:33 | Git |
| drwxrwxr-x | 3 | neko | neko | 23 | 9月 | 29 | 16:17 | source |

关于权限，可以参考：[Linux 权限](../../Linux%20%E6%9D%83%E9%99%90.md)
其中：

1. **链接数** 指的是 Unix 文件系统中的 inode 链接数，这个值只对目录有效，指的是目录下面的 **项目数量**
2. **用户组** 在这个地方通常每个用户会对应有一个同名的组，这个组也有可能是别的组，比如 wheel（RHEL 系 Linux 系统的超级管理员组组名）
3. **最后更新时间** 是根据本机的时区来计算的，如果是通过 SSH 访问的机器，这个时间对应的是服务器的时间，并非是本地计算机的时间
