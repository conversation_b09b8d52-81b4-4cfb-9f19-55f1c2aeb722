# `mv` 剪贴、移动

## 说明

**m**o**v**e，移动的含义。使用这个命令可以把文件移动到别的地方（重命名），这个操作对于同一个分区、硬盘的文件而言十分快速，因为只需要改变在本文件系统中的路径地址即可，如果需要跨分区、甚至是跨硬盘设备的话，效率和复制文件一致。

## 语法

```shell
mv <源路径> <目标路径>
```

### 示例

1. 移动单个文件

```shell
mv meow meow2
```

2. 移动文件夹内文件到另一个文件夹

```shell
mv test/* test2/
```

## 参数

### 其他参数

| 参数 | 说明 |
| ---- | ---- |
| **-b** | 当目标文件或目录存在时，在执行覆盖前，会为其创建一个备份 |
| **-i** | 如果指定移动的源目录或文件与目标的目录或文件同名，则会先询问是否覆盖旧文件，输入 y 表示直接覆盖，输入 n 表示取消该操作 |
| **-f** | 如果指定移动的源目录或文件与目标的目录或文件同名，不会询问，直接覆盖旧文件 |
| **-n** | 不要覆盖任何已存在的文件或目录 |
| **-u** | 当源文件比目标文件新或者目标文件不存在时，才执行移动操作 |
