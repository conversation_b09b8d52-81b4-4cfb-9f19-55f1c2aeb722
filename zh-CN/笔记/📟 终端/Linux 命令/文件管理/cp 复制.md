# `cp` 复制

## 说明

**c**o**p**y，复制的含义。使用这个命令可以把文件复制到别的地方，这个操作可能会很慢，因为需要把数据和路径一起变更，相比剪贴命令而言可能会慢很多，但是在同一个分区、硬盘内操作的话，依然是十分迅速的。

### 语法

```shell
cp [参数] <源路径> <目标路径>
```

### 示例

1. 复制单个文件

```shell
cp meow meow2
```

2. 移动文件夹内文件到另一个文件夹

```shell
cp test1/* test2/
```

## 参数

### 复制时包含子文件夹中的文件 - 参数 r

```shell
cp -r test1/* test2/
```

### 其他参数

| 参数 | 说明 |
| ---- | ---- |
| **-a** | 此选项通常在复制目录时使用，它保留链接、文件属性，并复制目录下的所有内容。其作用等于dpR参数组合 |
| **-d** | 复制时保留链接。这里所说的链接相当于 Windows 系统中的快捷方式 |
| **-f** | 覆盖已经存在的目标文件而不给出提示 |
| **-i** | 与 **-f** 选项相反，在覆盖目标文件之前给出提示，要求用户确认是否覆盖，回答 y 时目标文件将被覆盖 |
| **-p** | 除复制文件的内容外，还把修改时间和访问权限也复制到新文件中 |
| **-r** | 若给出的源文件是一个目录文件，此时将复制该目录下所有的子目录和文件 |
| **-l** | 不复制文件，只是生成链接文件 |
| **-u** | 此选项会对比文件，如果有差异才复制 |
| **-s** | 符号链接（symbolic link）（快捷方式）注: 与**-l** 的硬连接不同 |

