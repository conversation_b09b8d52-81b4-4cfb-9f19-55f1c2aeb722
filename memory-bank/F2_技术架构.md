# 🏗️ Nólëbase 技术架构详解

## 📁 系统分层架构

### 🎯 架构概览
```
┌─────────────────────────────────────────┐
│              用户界面层                    │
│    Vue 3 + UnoCSS + 自定义组件           │
├─────────────────────────────────────────┤
│              应用逻辑层                    │
│    VitePress + Nolebase 插件集成         │
├─────────────────────────────────────────┤
│              构建工具层                    │
│    Vite + TypeScript + pnpm             │
├─────────────────────────────────────────┤
│              内容处理层                    │
│    Markdown-it + 各种插件                │
├─────────────────────────────────────────┤
│              数据存储层                    │
│    Git + Markdown 文件 + 静态资源        │
└─────────────────────────────────────────┘
```

## 🔧 核心组件架构

### 📦 依赖关系图
```mermaid
graph TD
    A[VitePress] --> B[Vue 3]
    A --> C[Vite]
    A --> D[Markdown-it]
    
    E[@nolebase/integrations] --> A
    E --> F[双向链接插件]
    E --> G[阅读体验插件]
    E --> H[Git变更日志插件]
    E --> I[页面属性插件]
    
    C --> J[UnoCSS]
    C --> K[TypeScript]
    C --> L[unplugin-vue-components]
    
    D --> M[markdown-it-footnote]
    D --> N[markdown-it-mathjax3]
    D --> O[@hedgedoc/markdown-it-plugins]
```

### 🎨 主题架构
```typescript
// 主题扩展结构
const ExtendedTheme: Theme = {
  extends: DefaultTheme,
  Layout: () => {
    return h(DefaultTheme.Layout, null, {
      'doc-top': () => [
        h(NolebaseHighlightTargetedHeading),
        h(Gitcus),
      ],
      'doc-footer-before': () => [
        h(DocFooter),
      ],
      'nav-bar-content-after': () => [
        h(NolebaseEnhancedReadabilitiesMenu),
        h(Share),
      ],
    })
  },
}
```

## 🔌 插件系统设计

### 📋 核心插件列表
| 插件名称 | 版本 | 功能描述 |
|---------|------|----------|
| `@nolebase/integrations` | 2.18.1 | 核心集成包 |
| `@nolebase/markdown-it-bi-directional-links` | 2.18.1 | 双向链接支持 |
| `@nolebase/vitepress-plugin-enhanced-readabilities` | 2.18.1 | 阅读体验增强 |
| `@nolebase/vitepress-plugin-git-changelog` | 2.18.1 | Git变更日志 |
| `@nolebase/vitepress-plugin-highlight-targeted-heading` | 2.18.1 | 标题高亮 |
| `@nolebase/vitepress-plugin-page-properties` | 2.18.1 | 页面属性 |
| `@nolebase/vitepress-plugin-sidebar` | 2.18.1 | 侧边栏生成 |

### 🔄 插件加载流程
```typescript
// 1. Vite 配置中预设插件
const nolebase = presetVite({
  gitChangelog: { /* 配置 */ },
  pageProperties: { /* 配置 */ },
})

// 2. VitePress 配置中集成 Markdown 插件
const nolebaseMarkdown = presetMarkdownIt()
await nolebaseMarkdown.install(md)

// 3. 主题中集成客户端插件
const nolebaseClient = presetClient({
  enhancedReadabilities: { /* 配置 */ },
})
```

## 🗂️ 数据模型设计

### 📄 页面元数据结构
```typescript
interface PageMeta {
  tags: string[]           // 标签系统
  progress: number         // 阅读进度
  title: string           // 页面标题
  description: string     // 页面描述
  author: string[]        // 作者信息
  created: Date           // 创建时间
  updated: Date           // 更新时间
}
```

### 🏷️ 标签系统设计
```yaml
# Frontmatter 标签配置
---
tags:
  - 根层级
  - 另一个根层级/某个标签
  - 开发/前端/Vue
  - 软件/Obsidian/插件
---
```

## 🔍 搜索架构

### 🎯 本地搜索实现
```typescript
// 搜索配置
search: {
  provider: 'local',
  options: {
    // 自定义渲染函数，支持标签和标题搜索
    _render(src, env, md) {
      let html = md.render(src, env)
      let { frontmatter, content } = env
      
      // 处理标签、标题、内容
      return [headingPart, tagsPart, contentPart]
    }
  }
}
```

## 🚀 构建与部署架构

### 📦 构建流程
```mermaid
graph LR
    A[Markdown 源文件] --> B[Markdown-it 处理]
    B --> C[Vue 组件渲染]
    C --> D[Vite 构建]
    D --> E[静态文件输出]
    E --> F[部署到托管平台]
```

### 🌐 部署支持
- **Netlify**: 主要部署平台
- **Vercel**: 备选部署方案
- **GitHub Pages**: 开源项目首选
- **Cloudflare Pages**: 全球CDN加速

## 🔒 安全设计

### 🛡️ 内容安全
- **XSS 防护**: Vue 3 内置安全机制
- **CSRF 防护**: 静态站点天然免疫
- **内容验证**: Markdown 语法限制

### 🔐 访问控制
- **公开访问**: 静态站点公开可访问
- **源码保护**: Git 仓库权限控制
- **API 安全**: 无后端 API，安全性高

## ⚡ 性能优化

### 🚀 加载优化
- **代码分割**: Vite 自动代码分割
- **懒加载**: 图片和组件懒加载
- **缓存策略**: 浏览器缓存 + CDN 缓存
- **预加载**: 关键资源预加载

### 📊 构建优化
- **Tree Shaking**: 自动移除未使用代码
- **压缩优化**: Gzip/Brotli 压缩
- **资源优化**: 图片压缩和格式优化
- **Bundle 分析**: 构建产物分析
