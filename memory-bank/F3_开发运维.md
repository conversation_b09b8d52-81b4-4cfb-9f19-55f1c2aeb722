# 🛠️ Nólëbase 开发运维指南

## 🚀 环境搭建

### 📋 系统要求
- **Node.js**: v18+ (推荐 LTS 版本)
- **包管理器**: pnpm 10.14.0+
- **Git**: 用于版本控制
- **Python**: 用于 sharp 图片处理依赖

### 🔧 快速启动
```bash
# 1. 克隆项目
git clone https://github.com/nolebase/nolebase
cd nolebase

# 2. 安装依赖
pnpm install

# 3. 启动开发服务器
pnpm docs:dev

# 4. 构建生产版本
pnpm docs:build

# 5. 预览构建结果
pnpm docs:serve
```

### 🐧 Linux/macOS 环境
```bash
# 使用 Homebrew 安装 Node.js
brew install node

# 启用 corepack 并安装 pnpm
corepack enable
corepack prepare pnpm@latest --activate
pnpm setup
```

### 🪟 Windows 环境
```powershell
# 使用 Scoop 包管理器
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
Invoke-RestMethod -Uri https://get.scoop.sh | Invoke-Expression

# 安装必要工具
scoop install nodejs python

# 配置 pnpm
corepack enable
corepack prepare pnpm@latest --activate
pnpm setup
```

## 📁 项目结构详解

```
nolebase/
├── 📁 .vitepress/              # VitePress 配置目录
│   ├── 📄 config.ts           # 主配置文件
│   ├── 📄 head.ts             # HTML head 配置
│   ├── 📁 theme/              # 自定义主题
│   │   ├── 📄 index.ts        # 主题入口
│   │   └── 📁 components/     # 自定义组件
│   └── 📁 styles/             # 样式文件
├── 📁 zh-CN/                  # 中文内容目录
│   ├── 📄 index.md            # 首页
│   ├── 📄 toc.md              # 目录页
│   ├── 📁 笔记/               # 笔记分类
│   ├── 📁 编目 Catalog/       # 编目分类
│   └── 📁 视图/               # 视图分类
├── 📁 metadata/               # 元数据配置
├── 📁 scripts/                # 构建脚本
├── 📁 public/                 # 静态资源
├── 📄 package.json            # 项目配置
├── 📄 vite.config.ts          # Vite 配置
├── 📄 uno.config.ts           # UnoCSS 配置
└── 📄 pnpm-workspace.yaml     # pnpm 工作空间配置
```

## 🔄 开发工作流

### 📝 内容创作流程
```mermaid
graph LR
    A[Obsidian 编辑] --> B[Git 提交]
    B --> C[GitHub Actions]
    C --> D[自动构建]
    D --> E[部署到 Netlify]
    E --> F[在线访问]
```

### 🎯 开发最佳实践
1. **分支管理**: 使用 Git Flow 工作流
2. **提交规范**: 遵循 Conventional Commits
3. **代码审查**: Pull Request 必须经过审查
4. **测试验证**: 本地测试后再提交

### 📋 NPM Scripts 说明
```json
{
  "scripts": {
    "dev": "pnpm run docs:dev",           // 开发服务器
    "build": "pnpm run docs:build",       // 构建生产版本
    "serve": "pnpm run docs:serve",       // 预览构建结果
    "docs:dev": "vitepress dev",          // VitePress 开发模式
    "docs:build": "vitepress build",      // VitePress 构建
    "docs:serve": "vitepress serve",      // VitePress 预览
    "update": "tsx scripts/update.ts"     // 更新脚本
  }
}
```

## 🧪 测试策略

### 🔍 测试类型
- **构建测试**: 确保项目能正常构建
- **链接测试**: 检查内部链接有效性
- **性能测试**: 页面加载速度测试
- **兼容性测试**: 多浏览器兼容性

### 🚀 CI/CD 流程
```yaml
# GitHub Actions 工作流示例
name: Build and Deploy
on:
  push:
    branches: [main]
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: corepack enable
      - run: pnpm install
      - run: pnpm docs:build
      - name: Deploy to Netlify
        uses: netlify/actions/build@master
```

## 🚀 部署配置

### 🌐 Netlify 部署
```toml
# netlify.toml
[build]
  command = "pnpm docs:build"
  publish = ".vitepress/dist"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### ⚡ Vercel 部署
```json
{
  "buildCommand": "pnpm docs:build",
  "outputDirectory": ".vitepress/dist",
  "framework": "vitepress"
}
```

### 📊 GitHub Pages 部署
```yaml
# .github/workflows/deploy.yml
name: Deploy to GitHub Pages
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: pnpm install
      - run: pnpm docs:build
      - uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: .vitepress/dist
```

## 🔧 配置管理

### ⚙️ 环境变量
```bash
# .env.local
VITE_SITE_URL=https://nolebase.ayaka.io
VITE_GITHUB_REPO=https://github.com/nolebase/nolebase
VITE_DISCORD_LINK=https://discord.gg/XuNFDcDZGj
```

### 🎨 主题配置
```typescript
// .vitepress/theme/index.ts
const nolebase = presetClient({
  enhancedReadabilities: {
    options: {
      layoutSwitch: {
        defaultMode: 4,                    // 默认布局模式
      },
      spotlight: {
        defaultToggle: true,               // 默认开启聚光灯
        hoverBlockColor: 'rgb(240 197 52 / 7%)',
      },
    },
  },
})
```

## 📊 监控运维

### 📈 性能监控
- **Plausible.io**: 页面访问统计
- **Lighthouse**: 性能评分监控
- **Web Vitals**: 核心性能指标

### 🔍 错误监控
- **构建错误**: GitHub Actions 通知
- **链接检查**: 定期检查死链
- **依赖更新**: Dependabot 自动更新

### 📋 维护清单
- [ ] 定期更新依赖包
- [ ] 检查死链和图片
- [ ] 备份重要内容
- [ ] 监控网站性能
- [ ] 更新文档结构

## 🛡️ 故障处理

### 🚨 常见问题
1. **构建失败**: 检查依赖版本兼容性
2. **图片加载失败**: 检查图片路径和格式
3. **链接错误**: 使用相对路径
4. **样式异常**: 清除缓存重新构建

### 🔧 故障排查步骤
```bash
# 1. 清除缓存
rm -rf node_modules .vitepress/cache
pnpm install

# 2. 检查配置
pnpm docs:dev --debug

# 3. 构建测试
pnpm docs:build --debug

# 4. 检查输出
ls -la .vitepress/dist
```

## 📚 最佳实践

### ✅ 开发规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 格式化代码
- 编写清晰的提交信息

### 🎯 性能优化
- 优化图片大小和格式
- 使用懒加载减少初始加载
- 合理使用缓存策略
- 监控构建产物大小
