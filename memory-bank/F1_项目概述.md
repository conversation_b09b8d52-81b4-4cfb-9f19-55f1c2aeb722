# 📚 Nólëbase 项目概述

## 🎯 项目简介
**Nólëbase** 是一个现代化的知识库项目，融合了 Obsidian 的强大编辑能力和 VitePress 的静态站点生成优势。项目名取自昆雅语 nólë（知识）和英文 base（基础），意为"知识库"。

## 🏗️ 核心技术栈
- **静态站点生成器**: VitePress 2.0.0-alpha.9
- **前端框架**: Vue 3.5.18
- **构建工具**: Vite 7.0.6 + pnpm 工作空间
- **样式方案**: UnoCSS 66.3.3 + 自定义 CSS
- **Markdown 增强**: markdown-it + 多个插件
- **开发语言**: TypeScript 5.9.2
- **包管理器**: pnpm 10.14.0

## 🌟 核心特性
### 📝 内容管理
- 🌈 **多样化内容**: 生活、技术、回忆、畅想等多领域知识
- 📃 **纯 Markdown**: 所有内容均为 Markdown 格式
- 🔗 **双向链接**: 支持 Obsidian 风格的 `[[链接]]` 语法
- 🏷️ **标签系统**: 完善的分类和索引机制

### 🚀 技术特性
- ⚡ **快速构建**: 基于 Vite 的极速开发体验
- 🔍 **本地搜索**: 内置全文搜索功能
- 📱 **响应式设计**: 完美适配各种设备
- 🎨 **主题定制**: 支持亮色/暗色主题切换
- 🔌 **插件生态**: 丰富的 @nolebase 插件系列

### 🛠️ 开发特性
- 🔄 **热更新**: 实时预览编辑效果
- 📊 **Git 集成**: 自动生成变更日志和作者信息
- 🌐 **多语言**: 完善的国际化支持
- 🚀 **部署友好**: 支持多种静态托管平台

## 🎯 核心价值主张
1. **双重体验**: 本地 Obsidian 编辑 + 在线 VitePress 分享
2. **技术先进**: 使用最新的前端技术栈
3. **开源生态**: 活跃的社区和插件生态
4. **易于使用**: 简单的配置和部署流程

## 📈 使用场景
- 📚 **个人知识库**: 记录学习笔记、生活感悟
- 👥 **团队文档**: 技术文档、项目记录
- 🌐 **在线分享**: 将知识库发布为网站
- 🔬 **学术研究**: 论文笔记、研究资料整理

## 🏆 项目优势
- **性能优异**: 基于 Vite 的快速构建和热更新
- **SEO 友好**: 静态站点生成，搜索引擎优化
- **开发体验**: TypeScript + Vue 3 组合式 API
- **部署简单**: 支持 Netlify、Vercel、GitHub Pages 等
- **社区活跃**: 45+ 项目使用，3w+ 页面浏览量

## 🔗 相关链接
- **官网**: https://nolebase.ayaka.io
- **GitHub**: https://github.com/nolebase/nolebase
- **集成插件**: https://nolebase-integrations.ayaka.io
- **Discord**: https://discord.gg/XuNFDcDZGj

## 📊 项目状态
- **开源协议**: MIT License (代码) + CC BY-SA 4.0 (文档)
- **维护状态**: 活跃开发中
- **社区规模**: 中等规模，持续增长
- **技术成熟度**: 生产可用，功能完善
